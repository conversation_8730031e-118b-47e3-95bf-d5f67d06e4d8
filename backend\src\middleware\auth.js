const jwt = require('jsonwebtoken');
const { pool } = require('../config/database');
require('dotenv').config();

// Generate JWT tokens
const generateTokens = (payload) => {
  const accessToken = jwt.sign(payload, process.env.JWT_SECRET, {
    expiresIn: process.env.JWT_EXPIRES_IN,
    issuer: 'plumbing-parts-app',
    audience: 'plumbing-parts-users'
  });

  const refreshToken = jwt.sign(
    { userId: payload.userId, tokenVersion: payload.tokenVersion },
    process.env.JWT_REFRESH_SECRET,
    {
      expiresIn: process.env.JWT_REFRESH_EXPIRES_IN,
      issuer: 'plumbing-parts-app'
    }
  );

  return { accessToken, refreshToken };
};

// Verify JWT token
const verifyToken = (token, secret) => {
  return new Promise((resolve, reject) => {
    jwt.verify(token, secret, (err, decoded) => {
      if (err) {
        reject(err);
      } else {
        resolve(decoded);
      }
    });
  });
};

// Authentication middleware
const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
      return res.status(401).json({
        success: false,
        error: { 
          code: 'NO_TOKEN', 
          message: 'Access token required' 
        }
      });
    }

    const decoded = await verifyToken(token, process.env.JWT_SECRET);
    
    // Check if user still exists and is active
    const userQuery = 'SELECT * FROM users WHERE id = $1 AND is_active = true';
    const userResult = await pool.query(userQuery, [decoded.userId]);
    
    if (userResult.rows.length === 0) {
      return res.status(401).json({
        success: false,
        error: { 
          code: 'INVALID_USER', 
          message: 'User not found or inactive' 
        }
      });
    }

    const user = userResult.rows[0];

    // Check token version for revocation
    if (decoded.tokenVersion !== user.token_version) {
      return res.status(401).json({
        success: false,
        error: { 
          code: 'TOKEN_REVOKED', 
          message: 'Token has been revoked' 
        }
      });
    }

    // Add user to request object
    req.user = {
      id: user.id,
      email: user.email,
      firstName: user.first_name,
      lastName: user.last_name,
      role: user.role,
      companyName: user.company_name
    };

    next();
  } catch (error) {
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        error: { 
          code: 'TOKEN_EXPIRED', 
          message: 'Access token expired' 
        }
      });
    }

    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        error: { 
          code: 'INVALID_TOKEN', 
          message: 'Invalid access token' 
        }
      });
    }

    console.error('Authentication error:', error);
    return res.status(500).json({
      success: false,
      error: { 
        code: 'AUTH_ERROR', 
        message: 'Authentication failed' 
      }
    });
  }
};

// Optional authentication middleware (doesn't fail if no token)
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
      req.user = null;
      return next();
    }

    const decoded = await verifyToken(token, process.env.JWT_SECRET);
    const userQuery = 'SELECT * FROM users WHERE id = $1 AND is_active = true';
    const userResult = await pool.query(userQuery, [decoded.userId]);
    
    if (userResult.rows.length > 0) {
      const user = userResult.rows[0];
      req.user = {
        id: user.id,
        email: user.email,
        firstName: user.first_name,
        lastName: user.last_name,
        role: user.role,
        companyName: user.company_name
      };
    } else {
      req.user = null;
    }

    next();
  } catch (error) {
    req.user = null;
    next();
  }
};

// Role-based authorization middleware
const requireRole = (roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: { 
          code: 'UNAUTHORIZED', 
          message: 'Authentication required' 
        }
      });
    }

    const userRoles = Array.isArray(roles) ? roles : [roles];
    
    if (!userRoles.includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        error: { 
          code: 'INSUFFICIENT_PERMISSIONS', 
          message: 'Insufficient permissions' 
        }
      });
    }

    next();
  };
};

// Refresh token middleware
const refreshTokenMiddleware = async (req, res, next) => {
  try {
    const { refreshToken } = req.body;

    if (!refreshToken) {
      return res.status(401).json({
        success: false,
        error: { 
          code: 'NO_REFRESH_TOKEN', 
          message: 'Refresh token required' 
        }
      });
    }

    const decoded = await verifyToken(refreshToken, process.env.JWT_REFRESH_SECRET);
    
    // Check if user exists and token version matches
    const userQuery = 'SELECT * FROM users WHERE id = $1 AND is_active = true';
    const userResult = await pool.query(userQuery, [decoded.userId]);
    
    if (userResult.rows.length === 0) {
      return res.status(401).json({
        success: false,
        error: { 
          code: 'INVALID_USER', 
          message: 'User not found or inactive' 
        }
      });
    }

    const user = userResult.rows[0];

    if (decoded.tokenVersion !== user.token_version) {
      return res.status(401).json({
        success: false,
        error: { 
          code: 'TOKEN_REVOKED', 
          message: 'Refresh token has been revoked' 
        }
      });
    }

    req.user = user;
    next();
  } catch (error) {
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        error: { 
          code: 'REFRESH_TOKEN_EXPIRED', 
          message: 'Refresh token expired' 
        }
      });
    }

    return res.status(401).json({
      success: false,
      error: { 
        code: 'INVALID_REFRESH_TOKEN', 
        message: 'Invalid refresh token' 
      }
    });
  }
};

module.exports = {
  generateTokens,
  verifyToken,
  authenticateToken,
  optionalAuth,
  requireRole,
  refreshTokenMiddleware
};
