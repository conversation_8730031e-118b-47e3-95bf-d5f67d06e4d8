{"name": "plumbingpartsapp", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"expo": "~53.0.13", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.4", "@react-navigation/native": "^7.0.15", "@react-navigation/stack": "^7.1.1", "@react-navigation/bottom-tabs": "^7.1.0", "expo-camera": "~16.0.8", "expo-image-picker": "~16.0.3", "expo-media-library": "~17.0.3", "expo-permissions": "~15.0.0", "expo-secure-store": "~14.0.0", "expo-constants": "~17.0.3", "react-native-safe-area-context": "^4.14.0", "react-native-screens": "^4.2.0", "react-native-gesture-handler": "~2.20.2", "react-native-reanimated": "~3.16.1", "react-native-vector-icons": "^10.2.0", "@expo/vector-icons": "^14.0.4", "axios": "^1.7.9", "react-native-async-storage": "^0.0.1", "@react-native-async-storage/async-storage": "^2.1.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "private": true}