{"version": 3, "sources": ["utils.ts"], "names": ["isConfigParam", "param", "name", "undefined", "Object", "filterConfig", "props", "validProps", "defaults", "filteredConfig", "key", "value", "transformIntoHandlerTags", "top", "left", "bottom", "right", "handlerIDs", "Platform", "OS", "map", "current", "filter", "handle", "handlerID", "handlerIDToTag", "handlerTag", "findNodeHandle", "node", "flushOperationsScheduled", "scheduleFlushOperations", "RNGestureHandlerModule", "flushOperations"], "mappings": ";;;;;;;;;;AACA;;AACA;;AACA;;AACA;;AACA;;;;AAEA,SAASA,aAAT,CAAuBC,KAAvB,EAAuCC,IAAvC,EAAqD;AACnD;AACA;AACA,SACED,KAAK,KAAKE,SAAV,KACCF,KAAK,KAAKG,MAAM,CAACH,KAAD,CAAhB,IACC,EAAE,gBAAiBA,KAAnB,CAFF,KAGAC,IAAI,KAAK,sBAHT,IAIAA,IAAI,KAAK,gBALX;AAOD;;AAEM,SAASG,YAAT,CACLC,KADK,EAELC,UAFK,EAGLC,QAAiC,GAAG,EAH/B,EAIL;AACA,QAAMC,cAAc,GAAG,EAAE,GAAGD;AAAL,GAAvB;;AACA,OAAK,MAAME,GAAX,IAAkBH,UAAlB,EAA8B;AAC5B,QAAII,KAAK,GAAGL,KAAK,CAACI,GAAD,CAAjB;;AACA,QAAIV,aAAa,CAACW,KAAD,EAAQD,GAAR,CAAjB,EAA+B;AAC7B,UAAIA,GAAG,KAAK,sBAAR,IAAkCA,GAAG,KAAK,SAA9C,EAAyD;AACvDC,QAAAA,KAAK,GAAGC,wBAAwB,CAACN,KAAK,CAACI,GAAD,CAAN,CAAhC;AACD,OAFD,MAEO,IAAIA,GAAG,KAAK,SAAR,IAAqB,OAAOC,KAAP,KAAiB,QAA1C,EAAoD;AACzDA,QAAAA,KAAK,GAAG;AAAEE,UAAAA,GAAG,EAAEF,KAAP;AAAcG,UAAAA,IAAI,EAAEH,KAApB;AAA2BI,UAAAA,MAAM,EAAEJ,KAAnC;AAA0CK,UAAAA,KAAK,EAAEL;AAAjD,SAAR;AACD;;AACDF,MAAAA,cAAc,CAACC,GAAD,CAAd,GAAsBC,KAAtB;AACD;AACF;;AACD,SAAOF,cAAP;AACD;;AAEM,SAASG,wBAAT,CAAkCK,UAAlC,EAAmD;AACxDA,EAAAA,UAAU,GAAG,oBAAQA,UAAR,CAAb;;AAEA,MAAIC,sBAASC,EAAT,KAAgB,KAApB,EAA2B;AACzB,WAAOF,UAAU,CACdG,GADI,CACA,CAAC;AAAEC,MAAAA;AAAF,KAAD,KAAmCA,OADnC,EAEJC,MAFI,CAEIC,MAAD,IAAiBA,MAFpB,CAAP;AAGD,GAPuD,CAQxD;;;AACA,SAAON,UAAU,CACdG,GADI,CAEFI,SAAD;AAAA;;AAAA,WACEC,iCAAeD,SAAf,4BAA6BA,SAAS,CAACH,OAAvC,uDAA6B,mBAAmBK,UAAhD,KAA8D,CAAC,CADjE;AAAA,GAFG,EAKJJ,MALI,CAKII,UAAD,IAAwBA,UAAU,GAAG,CALxC,CAAP;AAMD;;AAEM,SAASC,cAAT,CACLC,IADK,EAEkE;AACvE,MAAIV,sBAASC,EAAT,KAAgB,KAApB,EAA2B;AACzB,WAAOS,IAAP;AACD;;AACD,SAAO,iCAAiBA,IAAjB,CAAP;AACD;;AACD,IAAIC,wBAAwB,GAAG,KAA/B;;AAEO,SAASC,uBAAT,GAAmC;AACxC,MAAI,CAACD,wBAAL,EAA+B;AAC7BA,IAAAA,wBAAwB,GAAG,IAA3B;AACA,4CAAiB,MAAM;AACrBE,sCAAuBC,eAAvB;;AAEAH,MAAAA,wBAAwB,GAAG,KAA3B;AACD,KAJD;AAKD;AACF", "sourcesContent": ["import * as React from 'react';\nimport { Platform, findNodeHandle as findNodeHandleRN } from 'react-native';\nimport { handlerIDToTag } from './handlersRegistry';\nimport { toArray } from '../utils';\nimport RNGestureHandlerModule from '../RNGestureHandlerModule';\nimport { ghQueueMicrotask } from '../ghQueueMicrotask';\n\nfunction isConfigParam(param: unknown, name: string) {\n  // param !== Object(param) returns false if `param` is a function\n  // or an object and returns true if `param` is null\n  return (\n    param !== undefined &&\n    (param !== Object(param) ||\n      !('__isNative' in (param as Record<string, unknown>))) &&\n    name !== 'onHandlerStateChange' &&\n    name !== 'onGestureEvent'\n  );\n}\n\nexport function filterConfig(\n  props: Record<string, unknown>,\n  validProps: string[],\n  defaults: Record<string, unknown> = {}\n) {\n  const filteredConfig = { ...defaults };\n  for (const key of validProps) {\n    let value = props[key];\n    if (isConfigParam(value, key)) {\n      if (key === 'simultaneousHandlers' || key === 'waitFor') {\n        value = transformIntoHandlerTags(props[key]);\n      } else if (key === 'hitSlop' && typeof value !== 'object') {\n        value = { top: value, left: value, bottom: value, right: value };\n      }\n      filteredConfig[key] = value;\n    }\n  }\n  return filteredConfig;\n}\n\nexport function transformIntoHandlerTags(handlerIDs: any) {\n  handlerIDs = toArray(handlerIDs);\n\n  if (Platform.OS === 'web') {\n    return handlerIDs\n      .map(({ current }: { current: any }) => current)\n      .filter((handle: any) => handle);\n  }\n  // converts handler string IDs into their numeric tags\n  return handlerIDs\n    .map(\n      (handlerID: any) =>\n        handlerIDToTag[handlerID] || handlerID.current?.handlerTag || -1\n    )\n    .filter((handlerTag: number) => handlerTag > 0);\n}\n\nexport function findNodeHandle(\n  node: null | number | React.Component<any, any> | React.ComponentClass<any>\n): null | number | React.Component<any, any> | React.ComponentClass<any> {\n  if (Platform.OS === 'web') {\n    return node;\n  }\n  return findNodeHandleRN(node);\n}\nlet flushOperationsScheduled = false;\n\nexport function scheduleFlushOperations() {\n  if (!flushOperationsScheduled) {\n    flushOperationsScheduled = true;\n    ghQueueMicrotask(() => {\n      RNGestureHandlerModule.flushOperations();\n\n      flushOperationsScheduled = false;\n    });\n  }\n}\n"]}