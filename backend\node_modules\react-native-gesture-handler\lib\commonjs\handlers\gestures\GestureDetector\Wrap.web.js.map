{"version": 3, "sources": ["Wrap.web.tsx"], "names": ["Wrap", "children", "ref", "child", "React", "Children", "only", "clone", "cloneElement", "props", "display", "e", "Error", "AnimatedWrap"], "mappings": ";;;;;;;AAAA;;AAEA;;AACA;;;;;;AAEO,MAAMA,IAAI,gBAAG,uBAClB,CAAC;AAAEC,EAAAA;AAAF,CAAD,EAAeC,GAAf,KAAuB;AACrB,MAAI;AACF;AACA,UAAMC,KAAU,GAAGC,eAAMC,QAAN,CAAeC,IAAf,CAAoBL,QAApB,CAAnB;;AAEA,QAAI,yBAAYE,KAAZ,CAAJ,EAAwB;AACtB,YAAMI,KAAK,gBAAGH,eAAMI,YAAN,CACZL,KADY,EAEZ;AAAED,QAAAA;AAAF,OAFY,EAGZ;AACAC,MAAAA,KAAK,CAACM,KAAN,CAAYR,QAJA,CAAd;;AAOA,aAAOM,KAAP;AACD;;AAED,wBACE;AACE,MAAA,GAAG,EAAEL,GADP;AAEE,MAAA,KAAK,EAAE;AAAEQ,QAAAA,OAAO,EAAE;AAAX;AAFT,OAGGP,KAHH,CADF;AAOD,GAtBD,CAsBE,OAAOQ,CAAP,EAAU;AACV,UAAM,IAAIC,KAAJ,CACJ,uBACG,2KADH,CADI,CAAN;AAKD;AACF,CA/BiB,CAAb,C,CAkCP;AACA;;;AACO,MAAMC,YAAY,GAAGb,IAArB", "sourcesContent": ["import React, { forwardRef } from 'react';\nimport type { LegacyRef, PropsWithChildren } from 'react';\nimport { tagMessage } from '../../../utils';\nimport { isRNSVGNode } from '../../../web/utils';\n\nexport const Wrap = forwardRef<HTMLDivElement, PropsWithChildren<{}>>(\n  ({ children }, ref) => {\n    try {\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      const child: any = React.Children.only(children);\n\n      if (isRNSVGNode(child)) {\n        const clone = React.cloneElement(\n          child,\n          { ref },\n          // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n          child.props.children\n        );\n\n        return clone;\n      }\n\n      return (\n        <div\n          ref={ref as LegacyRef<HTMLDivElement>}\n          style={{ display: 'contents' }}>\n          {child}\n        </div>\n      );\n    } catch (e) {\n      throw new Error(\n        tagMessage(\n          `GestureDetector got more than one view as a child. If you want the gesture to work on multiple views, wrap them with a common parent and attach the gesture to that view.`\n        )\n      );\n    }\n  }\n);\n\n// On web we never take a path with <PERSON>animated,\n// therefore we can simply export Wrap\nexport const AnimatedWrap = Wrap;\n"]}