const rateLimit = require('express-rate-limit');
const helmet = require('helmet');
const { redisClient } = require('../config/database');

// Rate limiting configurations
const createRateLimiter = (windowMs, maxRequests, message = 'Too many requests') => {
  return rateLimit({
    windowMs,
    max: (req) => {
      // Dynamic limits based on user role
      if (req.user) {
        const role = req.user.role;
        switch (role) {
          case 'enterprise': return maxRequests * 5;
          case 'premium': return maxRequests * 2;
          case 'admin': return maxRequests * 10;
          case 'user': return maxRequests;
          default: return maxRequests;
        }
      }
      return Math.floor(maxRequests * 0.1); // Unauthenticated users get 10%
    },
    message: {
      success: false,
      error: {
        code: 'RATE_LIMIT_EXCEEDED',
        message: message,
        retryAfter: Math.ceil(windowMs / 1000)
      }
    },
    standardHeaders: true,
    legacyHeaders: false,
    keyGenerator: (req) => {
      return req.user ? `user:${req.user.id}` : req.ip;
    },
    skip: (req) => {
      // Skip rate limiting for health checks
      return req.path === '/health' || req.path === '/ready';
    }
  });
};

// Different rate limiters for different endpoints
const generalLimiter = createRateLimiter(
  60 * 60 * 1000, // 1 hour
  1000, // 1000 requests per hour
  'Too many requests, please try again later'
);

const authLimiter = createRateLimiter(
  15 * 60 * 1000, // 15 minutes
  5, // 5 attempts per 15 minutes
  'Too many authentication attempts, please try again later'
);

const imageLimiter = createRateLimiter(
  60 * 60 * 1000, // 1 hour
  50, // 50 uploads per hour
  'Too many image uploads, please try again later'
);

const searchLimiter = createRateLimiter(
  60 * 60 * 1000, // 1 hour
  200, // 200 searches per hour
  'Too many search requests, please try again later'
);

// Security headers middleware
const securityHeaders = helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
    },
  },
  crossOriginEmbedderPolicy: false,
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  }
});

// CORS configuration
const corsOptions = {
  origin: (origin, callback) => {
    const allowedOrigins = (process.env.CORS_ORIGIN || '').split(',').map(o => o.trim());
    
    // Allow requests with no origin (mobile apps, Postman, etc.)
    if (!origin) return callback(null, true);
    
    if (allowedOrigins.includes(origin) || process.env.NODE_ENV === 'development') {
      callback(null, true);
    } else {
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  exposedHeaders: ['X-RateLimit-Limit', 'X-RateLimit-Remaining', 'X-RateLimit-Reset']
};

// Request logging middleware
const requestLogger = (req, res, next) => {
  const start = Date.now();
  
  // Log request
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path} - IP: ${req.ip} - User: ${req.user?.id || 'anonymous'}`);
  
  // Log response when finished
  res.on('finish', () => {
    const duration = Date.now() - start;
    console.log(`${new Date().toISOString()} - ${req.method} ${req.path} - ${res.statusCode} - ${duration}ms`);
  });
  
  next();
};

// Error handling middleware
const errorHandler = (err, req, res, next) => {
  console.error('Error:', err);
  
  // Default error response
  let statusCode = 500;
  let errorResponse = {
    success: false,
    error: {
      code: 'INTERNAL_ERROR',
      message: 'An unexpected error occurred'
    }
  };
  
  // Handle specific error types
  if (err.name === 'ValidationError') {
    statusCode = 400;
    errorResponse.error = {
      code: 'VALIDATION_ERROR',
      message: err.message
    };
  } else if (err.name === 'UnauthorizedError') {
    statusCode = 401;
    errorResponse.error = {
      code: 'UNAUTHORIZED',
      message: 'Authentication required'
    };
  } else if (err.code === '23505') { // PostgreSQL unique violation
    statusCode = 409;
    errorResponse.error = {
      code: 'DUPLICATE_ENTRY',
      message: 'Resource already exists'
    };
  } else if (err.code === '23503') { // PostgreSQL foreign key violation
    statusCode = 400;
    errorResponse.error = {
      code: 'INVALID_REFERENCE',
      message: 'Referenced resource does not exist'
    };
  }
  
  // Add request ID for debugging in production
  if (process.env.NODE_ENV === 'production') {
    errorResponse.error.requestId = req.id || 'unknown';
  } else {
    // Include stack trace in development
    errorResponse.error.stack = err.stack;
  }
  
  res.status(statusCode).json(errorResponse);
};

// 404 handler
const notFoundHandler = (req, res) => {
  res.status(404).json({
    success: false,
    error: {
      code: 'NOT_FOUND',
      message: `Route ${req.method} ${req.path} not found`
    }
  });
};

// Security monitoring middleware
const securityMonitor = (req, res, next) => {
  // Monitor for suspicious patterns
  const suspiciousPatterns = [
    /(<script>|<\/script>)/gi,
    /(javascript:|vbscript:|onload=|onerror=)/gi,
    /(union.*select|select.*from|insert.*into|delete.*from)/gi,
    /(\.\.\/){3,}/g // Path traversal attempts
  ];
  
  const checkSuspicious = (str) => {
    return suspiciousPatterns.some(pattern => pattern.test(str));
  };
  
  // Check URL, query parameters, and body for suspicious content
  const suspicious = 
    checkSuspicious(req.url) ||
    checkSuspicious(JSON.stringify(req.query)) ||
    checkSuspicious(JSON.stringify(req.body));
  
  if (suspicious) {
    console.warn(`Suspicious request detected from ${req.ip}: ${req.method} ${req.url}`);
    
    // Log to security monitoring system (implement as needed)
    // securityLogger.warn('Suspicious request', { ip: req.ip, url: req.url, userAgent: req.get('User-Agent') });
    
    return res.status(400).json({
      success: false,
      error: {
        code: 'SUSPICIOUS_REQUEST',
        message: 'Request contains suspicious content'
      }
    });
  }
  
  next();
};

// IP whitelist middleware (for admin endpoints)
const ipWhitelist = (allowedIPs = []) => {
  return (req, res, next) => {
    const clientIP = req.ip || req.connection.remoteAddress;
    
    if (allowedIPs.length > 0 && !allowedIPs.includes(clientIP)) {
      console.warn(`Access denied for IP: ${clientIP}`);
      return res.status(403).json({
        success: false,
        error: {
          code: 'IP_NOT_ALLOWED',
          message: 'Access denied from this IP address'
        }
      });
    }
    
    next();
  };
};

module.exports = {
  generalLimiter,
  authLimiter,
  imageLimiter,
  searchLimiter,
  securityHeaders,
  corsOptions,
  requestLogger,
  errorHandler,
  notFoundHandler,
  securityMonitor,
  ipWhitelist
};
