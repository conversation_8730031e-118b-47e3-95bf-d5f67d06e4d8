{"version": 3, "sources": ["useMountReactions.ts"], "names": ["shouldUpdateDetector", "relation", "gesture", "undefined", "tag", "handlerTag", "useMountReactions", "updateDetector", "state", "MountRegistry", "addMountListener", "attachedGesture", "attachedGestures", "blocksHandlers", "config", "requireToFail", "simultaneousWith"], "mappings": ";;;;;;;AAAA;;AACA;;AAEA;;AAGA,SAASA,oBAAT,CACEC,QADF,EAEEC,OAFF,EAGE;AACA,MAAID,QAAQ,KAAKE,SAAjB,EAA4B;AAC1B,WAAO,KAAP;AACD;;AAED,OAAK,MAAMC,GAAX,IAAkB,qCAAyBH,QAAzB,CAAlB,EAAsD;AACpD,QAAIG,GAAG,KAAKF,OAAO,CAACG,UAApB,EAAgC;AAC9B,aAAO,IAAP;AACD;AACF;;AAED,SAAO,KAAP;AACD;;AAEM,SAASC,iBAAT,CACLC,cADK,EAELC,KAFK,EAGL;AACA,wBAAU,MAAM;AACd,WAAOC,6BAAcC,gBAAd,CAAgCR,OAAD,IAAa;AACjD;AACA;AACA;AACA,WAAK,MAAMS,eAAX,IAA8BH,KAAK,CAACI,gBAApC,EAAsD;AACpD,cAAMC,cAAc,GAAGF,eAAe,CAACG,MAAhB,CAAuBD,cAA9C;AACA,cAAME,aAAa,GAAGJ,eAAe,CAACG,MAAhB,CAAuBC,aAA7C;AACA,cAAMC,gBAAgB,GAAGL,eAAe,CAACG,MAAhB,CAAuBE,gBAAhD;;AAEA,YACEhB,oBAAoB,CAACa,cAAD,EAAiBX,OAAjB,CAApB,IACAF,oBAAoB,CAACe,aAAD,EAAgBb,OAAhB,CADpB,IAEAF,oBAAoB,CAACgB,gBAAD,EAAmBd,OAAnB,CAHtB,EAIE;AACAK,UAAAA,cAAc,GADd,CAGA;;AACA;AACD;AACF;AACF,KApBM,CAAP;AAqBD,GAtBD,EAsBG,CAACA,cAAD,EAAiBC,KAAjB,CAtBH;AAuBD", "sourcesContent": ["import { transformIntoHandlerTags } from '../../utils';\nimport { MountRegistry } from '../../../mountRegistry';\nimport { AttachedGestureState } from './types';\nimport { useEffect } from 'react';\nimport { GestureRef } from '../gesture';\n\nfunction shouldUpdateDetector(\n  relation: GestureRef[] | undefined,\n  gesture: { handlerTag: number }\n) {\n  if (relation === undefined) {\n    return false;\n  }\n\n  for (const tag of transformIntoHandlerTags(relation)) {\n    if (tag === gesture.handlerTag) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\nexport function useMountReactions(\n  updateDetector: () => void,\n  state: AttachedGestureState\n) {\n  useEffect(() => {\n    return MountRegistry.addMountListener((gesture) => {\n      // At this point the ref in the gesture config should be updated, so we can check if one of the gestures\n      // set in a relation with the gesture got mounted. If so, we need to update the detector to propagate\n      // the changes to the native side.\n      for (const attachedGesture of state.attachedGestures) {\n        const blocksHandlers = attachedGesture.config.blocksHandlers;\n        const requireToFail = attachedGesture.config.requireToFail;\n        const simultaneousWith = attachedGesture.config.simultaneousWith;\n\n        if (\n          shouldUpdateDetector(blocksHandlers, gesture) ||\n          shouldUpdateDetector(requireToFail, gesture) ||\n          shouldUpdateDetector(simultaneousWith, gesture)\n        ) {\n          updateDetector();\n\n          // We can safely return here, if any other gestures should be updated, they will be by the above call\n          return;\n        }\n      }\n    });\n  }, [updateDetector, state]);\n}\n"]}