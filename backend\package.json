{"name": "backend", "version": "1.0.0", "description": "", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"express": "^5.1.0", "jsonwebtoken": "^9.0.2", "pg": "^8.16.3", "redis": "^5.5.6"}, "devDependencies": {"nodemon": "^3.1.10", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1"}}