
const express = require('express');
const router = express.Router();
const { healthCheck, readinessCheck } = require('../controllers/healthController');

/**
 * @swagger
 * /health:
 *   get:
 *     description: Health check endpoint
 *     responses:
 *       200:
 *         description: Success
 */
router.get('/', healthCheck);
router.get('/health', healthCheck);

/**
 * @swagger
 * /ready:
 *   get:
 *     description: Readiness check endpoint
 *     responses:
 *       200:
 *         description: Service is ready
 *       503:
 *         description: Service is not ready
 */
router.get('/ready', readinessCheck);

module.exports = router;
