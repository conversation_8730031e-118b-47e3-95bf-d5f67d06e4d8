const Joi = require('joi');

// Validation schemas
const schemas = {
  // User registration schema
  userRegistration: Joi.object({
    email: Joi.string().email().required().max(255),
    password: Joi.string()
      .min(8)
      .max(128)
      .pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
      .required()
      .messages({
        'string.pattern.base': 'Password must contain at least one lowercase letter, one uppercase letter, one number, and one special character'
      }),
    firstName: Joi.string().min(1).max(100).required(),
    lastName: Joi.string().min(1).max(100).required(),
    companyName: Joi.string().max(200).optional().allow('')
  }),

  // User login schema
  userLogin: Joi.object({
    email: Joi.string().email().required(),
    password: Joi.string().required()
  }),

  // Refresh token schema
  refreshToken: Joi.object({
    refreshToken: Joi.string().required()
  }),

  // Image upload schema
  imageUpload: Joi.object({
    processLocal: Joi.boolean().default(true),
    maxWidth: Joi.number().integer().min(100).max(4000).optional(),
    maxHeight: Joi.number().integer().min(100).max(4000).optional()
  }),

  // Part search schema
  partSearch: Joi.object({
    q: Joi.string().min(1).max(100).optional(),
    category: Joi.string().max(50).optional(),
    material: Joi.string().max(50).optional(),
    size: Joi.string().max(50).optional(),
    page: Joi.number().integer().min(1).max(1000).default(1),
    limit: Joi.number().integer().min(1).max(100).default(20)
  }),

  // User profile update schema
  userProfileUpdate: Joi.object({
    firstName: Joi.string().min(1).max(100).optional(),
    lastName: Joi.string().min(1).max(100).optional(),
    companyName: Joi.string().max(200).optional().allow(''),
    preferences: Joi.object({
      defaultCurrency: Joi.string().length(3).optional(),
      notifications: Joi.object({
        priceAlerts: Joi.boolean().optional(),
        stockAlerts: Joi.boolean().optional(),
        newFeatures: Joi.boolean().optional()
      }).optional()
    }).optional()
  }),

  // Price alert schema
  priceAlert: Joi.object({
    targetPrice: Joi.number().positive().precision(2).required(),
    currency: Joi.string().length(3).default('USD'),
    notificationMethod: Joi.string().valid('push', 'email', 'both').default('push')
  }),

  // Supplier configuration schema
  supplierConfig: Joi.object({
    name: Joi.string().min(1).max(255).required(),
    apiEndpoint: Joi.string().uri().optional(),
    integrationType: Joi.string().valid('api', 'edi', 'file', 'scraping').required(),
    configuration: Joi.object().optional()
  })
};

// Generic validation middleware factory
const validateRequest = (schema, source = 'body') => {
  return (req, res, next) => {
    const data = source === 'query' ? req.query : 
                  source === 'params' ? req.params : 
                  req.body;

    const { error, value } = schema.validate(data, {
      abortEarly: false,
      stripUnknown: true,
      convert: true
    });

    if (error) {
      const details = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
        value: detail.context?.value
      }));

      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid request data',
          details
        }
      });
    }

    // Replace the original data with validated and sanitized data
    if (source === 'query') {
      req.query = value;
    } else if (source === 'params') {
      req.params = value;
    } else {
      req.body = value;
    }

    next();
  };
};

// Specific validation middlewares
const validateUserRegistration = validateRequest(schemas.userRegistration);
const validateUserLogin = validateRequest(schemas.userLogin);
const validateRefreshToken = validateRequest(schemas.refreshToken);
const validateImageUpload = validateRequest(schemas.imageUpload);
const validatePartSearch = validateRequest(schemas.partSearch, 'query');
const validateUserProfileUpdate = validateRequest(schemas.userProfileUpdate);
const validatePriceAlert = validateRequest(schemas.priceAlert);
const validateSupplierConfig = validateRequest(schemas.supplierConfig);

// File validation middleware
const validateImageFile = (req, res, next) => {
  if (!req.file) {
    return res.status(400).json({
      success: false,
      error: {
        code: 'NO_FILE',
        message: 'Image file is required'
      }
    });
  }

  const allowedTypes = (process.env.ALLOWED_FILE_TYPES || 'image/jpeg,image/png,image/webp').split(',');
  const maxSize = parseInt(process.env.MAX_FILE_SIZE) || 10485760; // 10MB default

  // Check file type
  if (!allowedTypes.includes(req.file.mimetype)) {
    return res.status(400).json({
      success: false,
      error: {
        code: 'INVALID_FILE_TYPE',
        message: `File type not allowed. Allowed types: ${allowedTypes.join(', ')}`
      }
    });
  }

  // Check file size
  if (req.file.size > maxSize) {
    return res.status(400).json({
      success: false,
      error: {
        code: 'FILE_TOO_LARGE',
        message: `File size exceeds maximum allowed size of ${maxSize} bytes`
      }
    });
  }

  next();
};

// UUID validation middleware
const validateUUID = (paramName) => {
  return (req, res, next) => {
    const uuid = req.params[paramName];
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    
    if (!uuidRegex.test(uuid)) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_UUID',
          message: `Invalid UUID format for parameter: ${paramName}`
        }
      });
    }
    
    next();
  };
};

// Sanitization helper functions
const sanitizeString = (str) => {
  if (typeof str !== 'string') return str;
  
  // Remove potentially dangerous characters
  return str
    .replace(/[<>]/g, '') // Remove angle brackets
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers
    .trim();
};

const sanitizeObject = (obj) => {
  if (typeof obj === 'string') {
    return sanitizeString(obj);
  }
  
  if (Array.isArray(obj)) {
    return obj.map(sanitizeObject);
  }
  
  if (obj && typeof obj === 'object') {
    const sanitized = {};
    for (const [key, value] of Object.entries(obj)) {
      sanitized[key] = sanitizeObject(value);
    }
    return sanitized;
  }
  
  return obj;
};

// General sanitization middleware
const sanitizeInput = (req, res, next) => {
  if (req.body) {
    req.body = sanitizeObject(req.body);
  }
  
  if (req.query) {
    req.query = sanitizeObject(req.query);
  }
  
  next();
};

module.exports = {
  schemas,
  validateRequest,
  validateUserRegistration,
  validateUserLogin,
  validateRefreshToken,
  validateImageUpload,
  validatePartSearch,
  validateUserProfileUpdate,
  validatePriceAlert,
  validateSupplierConfig,
  validateImageFile,
  validateUUID,
  sanitizeInput
};
