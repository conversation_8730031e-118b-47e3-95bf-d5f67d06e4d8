{"version": 3, "sources": ["Pressable.tsx"], "names": ["DEFAULT_LONG_PRESS_DURATION", "IS_TEST_ENV", "IS_FABRIC", "Pressable", "props", "testOnly_pressed", "hitSlop", "pressRetentionOffset", "delayHoverIn", "onHoverIn", "delayHoverOut", "onHoverOut", "delayLongPress", "unstable_pressDelay", "onPress", "onPressIn", "onPressOut", "onLongPress", "style", "children", "android_disableSound", "android_ripple", "disabled", "remainingProps", "pressedState", "setPressedState", "pressableRef", "isPressCallbackEnabled", "hasPassedBoundsChecks", "shouldPreventNativeEffects", "normalizedHitSlop", "normalizedPressRetentionOffset", "hoverInTimeout", "hoverOutTimeout", "hoverGesture", "Gesture", "Hover", "manualActivation", "cancelsTouchesInView", "onBegin", "event", "current", "clearTimeout", "setTimeout", "onFinalize", "pressDelayTimeoutRef", "isTouchPropagationAllowed", "deferredEventPayload", "pressInHandler", "handlingOnTouchesDown", "pressOutHandler", "nativeEvent", "touches", "length", "changedTouches", "longPressTimeoutRef", "onEndHandlingTouchesDown", "cancelledMidPress", "activateLongPress", "longPressMinDuration", "pressAndTouchGesture", "Long<PERSON>ress", "minDuration", "INT32_MAX", "maxDistance", "onTouchesDown", "measure", "_x", "_y", "width", "height", "at", "onTouchesUp", "onTouchesCancelled", "allTouches", "buttonGesture", "Native", "Platform", "OS", "onStart", "appliedHitSlop", "isPressableEnabled", "gestures", "gesture", "enabled", "runOnJS", "shouldCancelWhenOutside", "Simultaneous", "pointerStyle", "cursor", "styleProp", "pressed", "childrenProp", "rippleColor", "defaultRippleColor", "undefined", "unprocessedRippleColor", "color", "radius", "__DEV__"], "mappings": ";;;;;;;AAAA;;AACA;;AACA;;AAEA;;AAQA;;AACA;;AAOA;;AAEA;;;;;;;;;;AAEA,MAAMA,2BAA2B,GAAG,GAApC;AACA,MAAMC,WAAW,GAAG,wBAApB;AAEA,IAAIC,SAAyB,GAAG,IAAhC;;AAEe,SAASC,SAAT,CAAmBC,KAAnB,EAA0C;AAAA;;AACvD,QAAM;AACJC,IAAAA,gBADI;AAEJC,IAAAA,OAFI;AAGJC,IAAAA,oBAHI;AAIJC,IAAAA,YAJI;AAKJC,IAAAA,SALI;AAMJC,IAAAA,aANI;AAOJC,IAAAA,UAPI;AAQJC,IAAAA,cARI;AASJC,IAAAA,mBATI;AAUJC,IAAAA,OAVI;AAWJC,IAAAA,SAXI;AAYJC,IAAAA,UAZI;AAaJC,IAAAA,WAbI;AAcJC,IAAAA,KAdI;AAeJC,IAAAA,QAfI;AAgBJC,IAAAA,oBAhBI;AAiBJC,IAAAA,cAjBI;AAkBJC,IAAAA,QAlBI;AAmBJ,OAAGC;AAnBC,MAoBFnB,KApBJ;AAsBA,QAAM,CAACoB,YAAD,EAAeC,eAAf,IAAkC,qBAASpB,gBAAT,aAASA,gBAAT,cAASA,gBAAT,GAA6B,KAA7B,CAAxC;AAEA,QAAMqB,YAAY,GAAG,mBAAa,IAAb,CAArB,CAzBuD,CA2BvD;;AACA,QAAMC,sBAAsB,GAAG,mBAAgB,IAAhB,CAA/B;AACA,QAAMC,qBAAqB,GAAG,mBAAgB,KAAhB,CAA9B;AACA,QAAMC,0BAA0B,GAAG,mBAAgB,KAAhB,CAAnC;AAEA,QAAMC,iBAAyB,GAAG,oBAChC,MACE,OAAOxB,OAAP,KAAmB,QAAnB,GAA8B,0BAAcA,OAAd,CAA9B,GAAwDA,OAAxD,aAAwDA,OAAxD,cAAwDA,OAAxD,GAAmE,EAFrC,EAGhC,CAACA,OAAD,CAHgC,CAAlC;AAMA,QAAMyB,8BAAsC,GAAG,oBAC7C,MACE,OAAOxB,oBAAP,KAAgC,QAAhC,GACI,0BAAcA,oBAAd,CADJ,GAEKA,oBAFL,aAEKA,oBAFL,cAEKA,oBAFL,GAE6B,EAJc,EAK7C,CAACA,oBAAD,CAL6C,CAA/C;AAQA,QAAMyB,cAAc,GAAG,mBAAsB,IAAtB,CAAvB;AACA,QAAMC,eAAe,GAAG,mBAAsB,IAAtB,CAAxB;AAEA,QAAMC,YAAY,GAAG,oBACnB,MACEC,+BAAQC,KAAR,GACGC,gBADH,CACoB,IADpB,EAC0B;AAD1B,GAEGC,oBAFH,CAEwB,KAFxB,EAGGC,OAHH,CAGYC,KAAD,IAAW;AAClB,QAAIP,eAAe,CAACQ,OAApB,EAA6B;AAC3BC,MAAAA,YAAY,CAACT,eAAe,CAACQ,OAAjB,CAAZ;AACD;;AACD,QAAIjC,YAAJ,EAAkB;AAChBwB,MAAAA,cAAc,CAACS,OAAf,GAAyBE,UAAU,CACjC,MAAMlC,SAAN,aAAMA,SAAN,uBAAMA,SAAS,CAAG,oCAAwB+B,KAAxB,CAAH,CADkB,EAEjChC,YAFiC,CAAnC;AAIA;AACD;;AACDC,IAAAA,SAAS,SAAT,IAAAA,SAAS,WAAT,YAAAA,SAAS,CAAG,oCAAwB+B,KAAxB,CAAH,CAAT;AACD,GAfH,EAgBGI,UAhBH,CAgBeJ,KAAD,IAAW;AACrB,QAAIR,cAAc,CAACS,OAAnB,EAA4B;AAC1BC,MAAAA,YAAY,CAACV,cAAc,CAACS,OAAhB,CAAZ;AACD;;AACD,QAAI/B,aAAJ,EAAmB;AACjBuB,MAAAA,eAAe,CAACQ,OAAhB,GAA0BE,UAAU,CAClC,MAAMhC,UAAN,aAAMA,UAAN,uBAAMA,UAAU,CAAG,oCAAwB6B,KAAxB,CAAH,CADkB,EAElC9B,aAFkC,CAApC;AAIA;AACD;;AACDC,IAAAA,UAAU,SAAV,IAAAA,UAAU,WAAV,YAAAA,UAAU,CAAG,oCAAwB6B,KAAxB,CAAH,CAAV;AACD,GA5BH,CAFiB,EA+BnB,CAAChC,YAAD,EAAeE,aAAf,EAA8BD,SAA9B,EAAyCE,UAAzC,CA/BmB,CAArB;AAkCA,QAAMkC,oBAAoB,GAAG,mBAAsB,IAAtB,CAA7B;AACA,QAAMC,yBAAyB,GAAG,mBAAgB,KAAhB,CAAlC,CApFuD,CAsFvD;;AACA,QAAMC,oBAAoB,GAAG,mBAA8B,IAA9B,CAA7B;AAEA,QAAMC,cAAc,GAAG,wBACpBR,KAAD,IAA2B;AACzB,QAAIS,qBAAqB,CAACR,OAA1B,EAAmC;AACjCM,MAAAA,oBAAoB,CAACN,OAArB,GAA+BD,KAA/B;AACD;;AAED,QAAI,CAACM,yBAAyB,CAACL,OAA/B,EAAwC;AACtC;AACD;;AAEDM,IAAAA,oBAAoB,CAACN,OAArB,GAA+B,IAA/B;AAEA1B,IAAAA,SAAS,SAAT,IAAAA,SAAS,WAAT,YAAAA,SAAS,CAAGyB,KAAH,CAAT;AACAb,IAAAA,sBAAsB,CAACc,OAAvB,GAAiC,IAAjC;AACAI,IAAAA,oBAAoB,CAACJ,OAArB,GAA+B,IAA/B;AACAhB,IAAAA,eAAe,CAAC,IAAD,CAAf;AACD,GAhBoB,EAiBrB,CAACV,SAAD,CAjBqB,CAAvB;AAoBA,QAAMmC,eAAe,GAAG,wBACrBV,KAAD,IAA2B;AACzB,QACE,CAACZ,qBAAqB,CAACa,OAAvB,IACAD,KAAK,CAACW,WAAN,CAAkBC,OAAlB,CAA0BC,MAA1B,GACEb,KAAK,CAACW,WAAN,CAAkBG,cAAlB,CAAiCD,MAHrC,EAIE;AACA;AACD;;AAED,QAAIxC,mBAAmB,IAAIgC,oBAAoB,CAACJ,OAArB,KAAiC,IAA5D,EAAkE;AAChE;AACA;AACA;AACAC,MAAAA,YAAY,CAACG,oBAAoB,CAACJ,OAAtB,CAAZ;AACAO,MAAAA,cAAc,CAACR,KAAD,CAAd;AACD;;AAED,QAAIO,oBAAoB,CAACN,OAAzB,EAAkC;AAChC1B,MAAAA,SAAS,SAAT,IAAAA,SAAS,WAAT,YAAAA,SAAS,CAAGgC,oBAAoB,CAACN,OAAxB,CAAT;AACAM,MAAAA,oBAAoB,CAACN,OAArB,GAA+B,IAA/B;AACD;;AAEDzB,IAAAA,UAAU,SAAV,IAAAA,UAAU,WAAV,YAAAA,UAAU,CAAGwB,KAAH,CAAV;;AAEA,QAAIb,sBAAsB,CAACc,OAA3B,EAAoC;AAClC3B,MAAAA,OAAO,SAAP,IAAAA,OAAO,WAAP,YAAAA,OAAO,CAAG0B,KAAH,CAAP;AACD;;AAED,QAAIe,mBAAmB,CAACd,OAAxB,EAAiC;AAC/BC,MAAAA,YAAY,CAACa,mBAAmB,CAACd,OAArB,CAAZ;AACAc,MAAAA,mBAAmB,CAACd,OAApB,GAA8B,IAA9B;AACD;;AAEDK,IAAAA,yBAAyB,CAACL,OAA1B,GAAoC,KAApC;AACAb,IAAAA,qBAAqB,CAACa,OAAtB,GAAgC,KAAhC;AACAd,IAAAA,sBAAsB,CAACc,OAAvB,GAAiC,IAAjC;AACAhB,IAAAA,eAAe,CAAC,KAAD,CAAf;AACD,GAtCqB,EAuCtB,CAACX,OAAD,EAAUC,SAAV,EAAqBC,UAArB,EAAiCgC,cAAjC,EAAiDnC,mBAAjD,CAvCsB,CAAxB;AA0CA,QAAMoC,qBAAqB,GAAG,mBAAgB,KAAhB,CAA9B;AACA,QAAMO,wBAAwB,GAAG,mBAA4B,IAA5B,CAAjC;AACA,QAAMC,iBAAiB,GAAG,mBAAgB,KAAhB,CAA1B;AAEA,QAAMC,iBAAiB,GAAG,wBACvBlB,KAAD,IAA8B;AAC5B,QAAI,CAACM,yBAAyB,CAACL,OAA/B,EAAwC;AACtC;AACD;;AAED,QAAIb,qBAAqB,CAACa,OAA1B,EAAmC;AACjCxB,MAAAA,WAAW,SAAX,IAAAA,WAAW,WAAX,YAAAA,WAAW,CAAG,yCAA6BuB,KAA7B,CAAH,CAAX;AACAb,MAAAA,sBAAsB,CAACc,OAAvB,GAAiC,KAAjC;AACD;;AAED,QAAIc,mBAAmB,CAACd,OAAxB,EAAiC;AAC/BC,MAAAA,YAAY,CAACa,mBAAmB,CAACd,OAArB,CAAZ;AACAc,MAAAA,mBAAmB,CAACd,OAApB,GAA8B,IAA9B;AACD;AACF,GAfuB,EAgBxB,CAACxB,WAAD,CAhBwB,CAA1B;AAmBA,QAAMsC,mBAAmB,GAAG,mBAAsB,IAAtB,CAA5B;AACA,QAAMI,oBAAoB,GACxB,CAAC/C,cAAD,aAACA,cAAD,cAACA,cAAD,GAAmBZ,2BAAnB,KACCa,mBADD,aACCA,mBADD,cACCA,mBADD,GACwB,CADxB,CADF;AAIA,QAAM+C,oBAAoB,GAAG,oBAC3B,MACEzB,+BAAQ0B,SAAR,GACGC,WADH,CACeC,iBADf,EAC0B;AAD1B,GAEGC,WAFH,CAEeD,iBAFf,EAE0B;AAF1B,GAGGzB,oBAHH,CAGwB,KAHxB,EAIG2B,aAJH,CAIkBzB,KAAD,IAAW;AAAA;;AACxBS,IAAAA,qBAAqB,CAACR,OAAtB,GAAgC,IAAhC;AACA,6BAAAf,YAAY,CAACe,OAAb,gFAAsByB,OAAtB,CAA8B,CAACC,EAAD,EAAKC,EAAL,EAASC,KAAT,EAAgBC,MAAhB,KAA2B;AAAA;;AACvD,UACE,CAAC,+BACC;AACED,QAAAA,KADF;AAEEC,QAAAA;AAFF,OADD,EAKCxC,iBALD,EAMCU,KAAK,CAACc,cAAN,CAAqBiB,EAArB,CAAwB,CAAC,CAAzB,CAND,CAAD,IAQA3C,qBAAqB,CAACa,OARtB,IASAgB,iBAAiB,CAAChB,OAVpB,EAWE;AACAgB,QAAAA,iBAAiB,CAAChB,OAAlB,GAA4B,KAA5B;AACAe,QAAAA,wBAAwB,CAACf,OAAzB,GAAmC,IAAnC;AACAQ,QAAAA,qBAAqB,CAACR,OAAtB,GAAgC,KAAhC;AACA;AACD;;AAEDb,MAAAA,qBAAqB,CAACa,OAAtB,GAAgC,IAAhC,CAnBuD,CAqBvD;;AACA,UAAIc,mBAAmB,CAACd,OAApB,KAAgC,IAApC,EAA0C;AACxC;AACAc,QAAAA,mBAAmB,CAACd,OAApB,GAA8BE,UAAU,CACtC,MAAMe,iBAAiB,CAAClB,KAAD,CADe,EAEtCmB,oBAFsC,CAAxC;AAID;;AAED,UAAI9C,mBAAJ,EAAyB;AACvBgC,QAAAA,oBAAoB,CAACJ,OAArB,GAA+BE,UAAU,CAAC,MAAM;AAC9CK,UAAAA,cAAc,CAAC,yCAA6BR,KAA7B,CAAD,CAAd;AACD,SAFwC,EAEtC3B,mBAFsC,CAAzC;AAGD,OAJD,MAIO;AACLmC,QAAAA,cAAc,CAAC,yCAA6BR,KAA7B,CAAD,CAAd;AACD;;AAED,+BAAAgB,wBAAwB,CAACf,OAAzB,qFAAAe,wBAAwB;AACxBA,MAAAA,wBAAwB,CAACf,OAAzB,GAAmC,IAAnC;AACAQ,MAAAA,qBAAqB,CAACR,OAAtB,GAAgC,KAAhC;AACD,KAzCD;AA0CD,GAhDH,EAiDG+B,WAjDH,CAiDgBhC,KAAD,IAAW;AACtB,QAAIS,qBAAqB,CAACR,OAA1B,EAAmC;AACjCe,MAAAA,wBAAwB,CAACf,OAAzB,GAAmC,MACjCS,eAAe,CAAC,yCAA6BV,KAA7B,CAAD,CADjB;;AAEA;AACD,KALqB,CAMtB;AACA;;;AACA,QAAIO,oBAAoB,CAACN,OAArB,KAAiC,IAArC,EAA2C;AACzCZ,MAAAA,0BAA0B,CAACY,OAA3B,GAAqC,IAArC;AACD;;AACDS,IAAAA,eAAe,CAAC,yCAA6BV,KAA7B,CAAD,CAAf;AACD,GA7DH,EA8DGiC,kBA9DH,CA8DuBjC,KAAD,IAAW;AAC7Bb,IAAAA,sBAAsB,CAACc,OAAvB,GAAiC,KAAjC;;AAEA,QAAIQ,qBAAqB,CAACR,OAA1B,EAAmC;AACjCgB,MAAAA,iBAAiB,CAAChB,OAAlB,GAA4B,IAA5B;;AACAe,MAAAA,wBAAwB,CAACf,OAAzB,GAAmC,MACjCS,eAAe,CAAC,yCAA6BV,KAA7B,CAAD,CADjB;;AAEA;AACD;;AAED,QACE,CAACZ,qBAAqB,CAACa,OAAvB,IACAD,KAAK,CAACkC,UAAN,CAAiBrB,MAAjB,GAA0Bb,KAAK,CAACc,cAAN,CAAqBD,MAFjD,EAGE;AACA;AACD;;AAEDH,IAAAA,eAAe,CAAC,yCAA6BV,KAA7B,CAAD,CAAf;AACD,GAhFH,CAFyB,EAmF3B,CACEkB,iBADF,EAEEC,oBAFF,EAGE7B,iBAHF,EAIEkB,cAJF,EAKEE,eALF,EAMErC,mBANF,CAnF2B,CAA7B,CAnLuD,CAgRvD;;AACA,QAAM8D,aAAa,GAAG,oBACpB,MACExC,+BAAQyC,MAAR,GACGrC,OADH,CACW,MAAM;AACb;AACA,QAAIsC,sBAASC,EAAT,KAAgB,SAAhB,IAA6BD,sBAASC,EAAT,KAAgB,OAAjD,EAA0D;AACxDhC,MAAAA,yBAAyB,CAACL,OAA1B,GAAoC,IAApC;AACD;AACF,GANH,EAOGsC,OAPH,CAOW,MAAM;AACb,QAAIF,sBAASC,EAAT,KAAgB,KAApB,EAA2B;AACzBhC,MAAAA,yBAAyB,CAACL,OAA1B,GAAoC,IAApC;AACD,KAHY,CAKb;;;AACA,QAAIoC,sBAASC,EAAT,KAAgB,KAApB,EAA2B;AACzB;AACD;;AAED,QAAI/B,oBAAoB,CAACN,OAAzB,EAAkC;AAChCK,MAAAA,yBAAyB,CAACL,OAA1B,GAAoC,IAApC;;AAEA,UAAIb,qBAAqB,CAACa,OAA1B,EAAmC;AACjCO,QAAAA,cAAc,CAACD,oBAAoB,CAACN,OAAtB,CAAd;AACAM,QAAAA,oBAAoB,CAACN,OAArB,GAA+B,IAA/B;AACD,OAHD,MAGO;AACLS,QAAAA,eAAe,CAACH,oBAAoB,CAACN,OAAtB,CAAf;AACAK,QAAAA,yBAAyB,CAACL,OAA1B,GAAoC,KAApC;AACD;;AAED;AACD;;AAED,QAAIb,qBAAqB,CAACa,OAA1B,EAAmC;AACjCK,MAAAA,yBAAyB,CAACL,OAA1B,GAAoC,IAApC;AACA;AACD;;AAED,QAAIZ,0BAA0B,CAACY,OAA/B,EAAwC;AACtCZ,MAAAA,0BAA0B,CAACY,OAA3B,GAAqC,KAArC;AACA;AACD;;AAEDK,IAAAA,yBAAyB,CAACL,OAA1B,GAAoC,IAApC;AACD,GA1CH,CAFkB,EA6CpB,CAACO,cAAD,EAAiBE,eAAjB,CA7CoB,CAAtB;AAgDA,QAAM8B,cAAc,GAAG,sBACrBlD,iBADqB,EAErBC,8BAFqB,CAAvB;AAKA,QAAMkD,kBAAkB,GAAG3D,QAAQ,KAAK,IAAxC;AAEA,QAAM4D,QAAQ,GAAG,CAACP,aAAD,EAAgBf,oBAAhB,EAAsC1B,YAAtC,CAAjB;;AAEA,OAAK,MAAMiD,OAAX,IAAsBD,QAAtB,EAAgC;AAC9BC,IAAAA,OAAO,CAACC,OAAR,CAAgBH,kBAAhB;AACAE,IAAAA,OAAO,CAACE,OAAR,CAAgB,IAAhB;AACAF,IAAAA,OAAO,CAAC7E,OAAR,CAAgB0E,cAAhB;AACAG,IAAAA,OAAO,CAACG,uBAAR,CAAgCT,sBAASC,EAAT,KAAgB,KAAhB,GAAwB,KAAxB,GAAgC,IAAhE;AACD,GA/UsD,CAiVvD;;;AACAH,EAAAA,aAAa,CAACrE,OAAd,CAAsBwB,iBAAtB;;AAEA,QAAMqD,OAAO,GAAGhD,+BAAQoD,YAAR,CAAqB,GAAGL,QAAxB,CAAhB,CApVuD,CAsVvD;;;AACA,QAAMM,YAAkC,GACtCX,sBAASC,EAAT,KAAgB,KAAhB,GAAwB;AAAEW,IAAAA,MAAM,EAAE;AAAV,GAAxB,GAAgD,EADlD;AAGA,QAAMC,SAAS,GACb,OAAOxE,KAAP,KAAiB,UAAjB,GAA8BA,KAAK,CAAC;AAAEyE,IAAAA,OAAO,EAAEnE;AAAX,GAAD,CAAnC,GAAiEN,KADnE;AAGA,QAAM0E,YAAY,GAChB,OAAOzE,QAAP,KAAoB,UAApB,GACIA,QAAQ,CAAC;AAAEwE,IAAAA,OAAO,EAAEnE;AAAX,GAAD,CADZ,GAEIL,QAHN;AAKA,QAAM0E,WAAW,GAAG,oBAAQ,MAAM;AAAA;;AAChC,QAAI3F,SAAS,KAAK,IAAlB,EAAwB;AACtBA,MAAAA,SAAS,GAAG,uBAAZ;AACD;;AAED,UAAM4F,kBAAkB,GAAGzE,cAAc,GAAG0E,SAAH,GAAe,aAAxD;AACA,UAAMC,sBAAsB,4BAAG3E,cAAH,aAAGA,cAAH,uBAAGA,cAAc,CAAE4E,KAAnB,yEAA4BH,kBAAxD;AACA,WAAO5F,SAAS,GACZ8F,sBADY,GAEZ,+BAAaA,sBAAb,CAFJ;AAGD,GAVmB,EAUjB,CAAC3E,cAAD,CAViB,CAApB;AAYA,sBACE,6BAAC,gCAAD;AAAiB,IAAA,OAAO,EAAE8D;AAA1B,kBACE,6BAAC,6BAAD,eACM5D,cADN;AAEE,IAAA,GAAG,EAAEG,YAFP;AAGE,IAAA,OAAO,EAAEsD,cAHX;AAIE,IAAA,OAAO,EAAEC,kBAJX;AAKE,IAAA,kBAAkB,EAAE7D,oBAAF,aAAEA,oBAAF,cAAEA,oBAAF,GAA0B2E,SAL9C;AAME,IAAA,WAAW,EAAEF,WANf;AAOE,IAAA,YAAY,2BAAExE,cAAF,aAAEA,cAAF,uBAAEA,cAAc,CAAE6E,MAAlB,yEAA4BH,SAP1C;AAQE,IAAA,KAAK,EAAE,CAACP,YAAD,EAAeE,SAAf,CART;AASE,IAAA,gBAAgB,EAAEzF,WAAW,GAAGa,OAAH,GAAaiF,SAT5C;AAUE,IAAA,kBAAkB,EAAE9F,WAAW,GAAGc,SAAH,GAAegF,SAVhD;AAWE,IAAA,mBAAmB,EAAE9F,WAAW,GAAGe,UAAH,GAAgB+E,SAXlD;AAYE,IAAA,oBAAoB,EAAE9F,WAAW,GAAGgB,WAAH,GAAiB8E;AAZpD,MAaGH,YAbH,EAcGO,OAAO,gBACN,6BAAC,4CAAD;AAAuB,IAAA,KAAK,EAAC,KAA7B;AAAmC,IAAA,OAAO,EAAErE;AAA5C,IADM,GAEJ,IAhBN,CADF,CADF;AAsBD", "sourcesContent": ["import React, { useCallback, useMemo, useRef, useState } from 'react';\nimport { GestureObjects as Gesture } from '../../handlers/gestures/gestureObjects';\nimport { GestureDetector } from '../../handlers/gestures/GestureDetector';\nimport { PressableEvent, PressableProps } from './PressableProps';\nimport {\n  Insets,\n  Platform,\n  StyleProp,\n  View,\n  ViewStyle,\n  processColor,\n} from 'react-native';\nimport NativeButton from '../GestureHandlerButton';\nimport {\n  numberAsInset,\n  gestureToPressableEvent,\n  isTouchWithinInset,\n  gestureTouchToPressableEvent,\n  addInsets,\n} from './utils';\nimport { PressabilityDebugView } from '../../handlers/PressabilityDebugView';\nimport { GestureTouchEvent } from '../../handlers/gestureHandlerCommon';\nimport { INT32_MAX, isFabric, isTestEnv } from '../../utils';\n\nconst DEFAULT_LONG_PRESS_DURATION = 500;\nconst IS_TEST_ENV = isTestEnv();\n\nlet IS_FABRIC: null | boolean = null;\n\nexport default function Pressable(props: PressableProps) {\n  const {\n    testOnly_pressed,\n    hitSlop,\n    pressRetentionOffset,\n    delayHoverIn,\n    onHoverIn,\n    delayHoverOut,\n    onHoverOut,\n    delayLongPress,\n    unstable_pressDelay,\n    onPress,\n    onPressIn,\n    onPressOut,\n    onLongPress,\n    style,\n    children,\n    android_disableSound,\n    android_ripple,\n    disabled,\n    ...remainingProps\n  } = props;\n\n  const [pressedState, setPressedState] = useState(testOnly_pressed ?? false);\n\n  const pressableRef = useRef<View>(null);\n\n  // Disabled when onLongPress has been called\n  const isPressCallbackEnabled = useRef<boolean>(true);\n  const hasPassedBoundsChecks = useRef<boolean>(false);\n  const shouldPreventNativeEffects = useRef<boolean>(false);\n\n  const normalizedHitSlop: Insets = useMemo(\n    () =>\n      typeof hitSlop === 'number' ? numberAsInset(hitSlop) : (hitSlop ?? {}),\n    [hitSlop]\n  );\n\n  const normalizedPressRetentionOffset: Insets = useMemo(\n    () =>\n      typeof pressRetentionOffset === 'number'\n        ? numberAsInset(pressRetentionOffset)\n        : (pressRetentionOffset ?? {}),\n    [pressRetentionOffset]\n  );\n\n  const hoverInTimeout = useRef<number | null>(null);\n  const hoverOutTimeout = useRef<number | null>(null);\n\n  const hoverGesture = useMemo(\n    () =>\n      Gesture.Hover()\n        .manualActivation(true) // Stops Hover from blocking Native gesture activation on web\n        .cancelsTouchesInView(false)\n        .onBegin((event) => {\n          if (hoverOutTimeout.current) {\n            clearTimeout(hoverOutTimeout.current);\n          }\n          if (delayHoverIn) {\n            hoverInTimeout.current = setTimeout(\n              () => onHoverIn?.(gestureToPressableEvent(event)),\n              delayHoverIn\n            );\n            return;\n          }\n          onHoverIn?.(gestureToPressableEvent(event));\n        })\n        .onFinalize((event) => {\n          if (hoverInTimeout.current) {\n            clearTimeout(hoverInTimeout.current);\n          }\n          if (delayHoverOut) {\n            hoverOutTimeout.current = setTimeout(\n              () => onHoverOut?.(gestureToPressableEvent(event)),\n              delayHoverOut\n            );\n            return;\n          }\n          onHoverOut?.(gestureToPressableEvent(event));\n        }),\n    [delayHoverIn, delayHoverOut, onHoverIn, onHoverOut]\n  );\n\n  const pressDelayTimeoutRef = useRef<number | null>(null);\n  const isTouchPropagationAllowed = useRef<boolean>(false);\n\n  // iOS only: due to varying flow of gestures, events sometimes have to be saved for later use\n  const deferredEventPayload = useRef<PressableEvent | null>(null);\n\n  const pressInHandler = useCallback(\n    (event: PressableEvent) => {\n      if (handlingOnTouchesDown.current) {\n        deferredEventPayload.current = event;\n      }\n\n      if (!isTouchPropagationAllowed.current) {\n        return;\n      }\n\n      deferredEventPayload.current = null;\n\n      onPressIn?.(event);\n      isPressCallbackEnabled.current = true;\n      pressDelayTimeoutRef.current = null;\n      setPressedState(true);\n    },\n    [onPressIn]\n  );\n\n  const pressOutHandler = useCallback(\n    (event: PressableEvent) => {\n      if (\n        !hasPassedBoundsChecks.current ||\n        event.nativeEvent.touches.length >\n          event.nativeEvent.changedTouches.length\n      ) {\n        return;\n      }\n\n      if (unstable_pressDelay && pressDelayTimeoutRef.current !== null) {\n        // When delay is preemptively finished by lifting touches,\n        // we want to immediately activate it's effects - pressInHandler,\n        // even though we are located at the pressOutHandler\n        clearTimeout(pressDelayTimeoutRef.current);\n        pressInHandler(event);\n      }\n\n      if (deferredEventPayload.current) {\n        onPressIn?.(deferredEventPayload.current);\n        deferredEventPayload.current = null;\n      }\n\n      onPressOut?.(event);\n\n      if (isPressCallbackEnabled.current) {\n        onPress?.(event);\n      }\n\n      if (longPressTimeoutRef.current) {\n        clearTimeout(longPressTimeoutRef.current);\n        longPressTimeoutRef.current = null;\n      }\n\n      isTouchPropagationAllowed.current = false;\n      hasPassedBoundsChecks.current = false;\n      isPressCallbackEnabled.current = true;\n      setPressedState(false);\n    },\n    [onPress, onPressIn, onPressOut, pressInHandler, unstable_pressDelay]\n  );\n\n  const handlingOnTouchesDown = useRef<boolean>(false);\n  const onEndHandlingTouchesDown = useRef<(() => void) | null>(null);\n  const cancelledMidPress = useRef<boolean>(false);\n\n  const activateLongPress = useCallback(\n    (event: GestureTouchEvent) => {\n      if (!isTouchPropagationAllowed.current) {\n        return;\n      }\n\n      if (hasPassedBoundsChecks.current) {\n        onLongPress?.(gestureTouchToPressableEvent(event));\n        isPressCallbackEnabled.current = false;\n      }\n\n      if (longPressTimeoutRef.current) {\n        clearTimeout(longPressTimeoutRef.current);\n        longPressTimeoutRef.current = null;\n      }\n    },\n    [onLongPress]\n  );\n\n  const longPressTimeoutRef = useRef<number | null>(null);\n  const longPressMinDuration =\n    (delayLongPress ?? DEFAULT_LONG_PRESS_DURATION) +\n    (unstable_pressDelay ?? 0);\n\n  const pressAndTouchGesture = useMemo(\n    () =>\n      Gesture.LongPress()\n        .minDuration(INT32_MAX) // Stops long press from blocking native gesture\n        .maxDistance(INT32_MAX) // Stops long press from cancelling after set distance\n        .cancelsTouchesInView(false)\n        .onTouchesDown((event) => {\n          handlingOnTouchesDown.current = true;\n          pressableRef.current?.measure((_x, _y, width, height) => {\n            if (\n              !isTouchWithinInset(\n                {\n                  width,\n                  height,\n                },\n                normalizedHitSlop,\n                event.changedTouches.at(-1)\n              ) ||\n              hasPassedBoundsChecks.current ||\n              cancelledMidPress.current\n            ) {\n              cancelledMidPress.current = false;\n              onEndHandlingTouchesDown.current = null;\n              handlingOnTouchesDown.current = false;\n              return;\n            }\n\n            hasPassedBoundsChecks.current = true;\n\n            // In case of multiple touches, the first one starts long press gesture\n            if (longPressTimeoutRef.current === null) {\n              // Start long press gesture timer\n              longPressTimeoutRef.current = setTimeout(\n                () => activateLongPress(event),\n                longPressMinDuration\n              );\n            }\n\n            if (unstable_pressDelay) {\n              pressDelayTimeoutRef.current = setTimeout(() => {\n                pressInHandler(gestureTouchToPressableEvent(event));\n              }, unstable_pressDelay);\n            } else {\n              pressInHandler(gestureTouchToPressableEvent(event));\n            }\n\n            onEndHandlingTouchesDown.current?.();\n            onEndHandlingTouchesDown.current = null;\n            handlingOnTouchesDown.current = false;\n          });\n        })\n        .onTouchesUp((event) => {\n          if (handlingOnTouchesDown.current) {\n            onEndHandlingTouchesDown.current = () =>\n              pressOutHandler(gestureTouchToPressableEvent(event));\n            return;\n          }\n          // On iOS, short taps will make LongPress gesture call onTouchesUp before Native gesture calls onStart\n          // This variable ensures that onStart isn't detected as the first gesture since Pressable is pressed.\n          if (deferredEventPayload.current !== null) {\n            shouldPreventNativeEffects.current = true;\n          }\n          pressOutHandler(gestureTouchToPressableEvent(event));\n        })\n        .onTouchesCancelled((event) => {\n          isPressCallbackEnabled.current = false;\n\n          if (handlingOnTouchesDown.current) {\n            cancelledMidPress.current = true;\n            onEndHandlingTouchesDown.current = () =>\n              pressOutHandler(gestureTouchToPressableEvent(event));\n            return;\n          }\n\n          if (\n            !hasPassedBoundsChecks.current ||\n            event.allTouches.length > event.changedTouches.length\n          ) {\n            return;\n          }\n\n          pressOutHandler(gestureTouchToPressableEvent(event));\n        }),\n    [\n      activateLongPress,\n      longPressMinDuration,\n      normalizedHitSlop,\n      pressInHandler,\n      pressOutHandler,\n      unstable_pressDelay,\n    ]\n  );\n\n  // RNButton is placed inside ButtonGesture to enable Android's ripple and to capture non-propagating events\n  const buttonGesture = useMemo(\n    () =>\n      Gesture.Native()\n        .onBegin(() => {\n          // Android sets BEGAN state on press down\n          if (Platform.OS === 'android' || Platform.OS === 'macos') {\n            isTouchPropagationAllowed.current = true;\n          }\n        })\n        .onStart(() => {\n          if (Platform.OS === 'web') {\n            isTouchPropagationAllowed.current = true;\n          }\n\n          // iOS sets ACTIVE state on press down\n          if (Platform.OS !== 'ios') {\n            return;\n          }\n\n          if (deferredEventPayload.current) {\n            isTouchPropagationAllowed.current = true;\n\n            if (hasPassedBoundsChecks.current) {\n              pressInHandler(deferredEventPayload.current);\n              deferredEventPayload.current = null;\n            } else {\n              pressOutHandler(deferredEventPayload.current);\n              isTouchPropagationAllowed.current = false;\n            }\n\n            return;\n          }\n\n          if (hasPassedBoundsChecks.current) {\n            isTouchPropagationAllowed.current = true;\n            return;\n          }\n\n          if (shouldPreventNativeEffects.current) {\n            shouldPreventNativeEffects.current = false;\n            return;\n          }\n\n          isTouchPropagationAllowed.current = true;\n        }),\n    [pressInHandler, pressOutHandler]\n  );\n\n  const appliedHitSlop = addInsets(\n    normalizedHitSlop,\n    normalizedPressRetentionOffset\n  );\n\n  const isPressableEnabled = disabled !== true;\n\n  const gestures = [buttonGesture, pressAndTouchGesture, hoverGesture];\n\n  for (const gesture of gestures) {\n    gesture.enabled(isPressableEnabled);\n    gesture.runOnJS(true);\n    gesture.hitSlop(appliedHitSlop);\n    gesture.shouldCancelWhenOutside(Platform.OS === 'web' ? false : true);\n  }\n\n  // Uses different hitSlop, to activate on hitSlop area instead of pressRetentionOffset area\n  buttonGesture.hitSlop(normalizedHitSlop);\n\n  const gesture = Gesture.Simultaneous(...gestures);\n\n  // `cursor: 'pointer'` on `RNButton` crashes iOS\n  const pointerStyle: StyleProp<ViewStyle> =\n    Platform.OS === 'web' ? { cursor: 'pointer' } : {};\n\n  const styleProp =\n    typeof style === 'function' ? style({ pressed: pressedState }) : style;\n\n  const childrenProp =\n    typeof children === 'function'\n      ? children({ pressed: pressedState })\n      : children;\n\n  const rippleColor = useMemo(() => {\n    if (IS_FABRIC === null) {\n      IS_FABRIC = isFabric();\n    }\n\n    const defaultRippleColor = android_ripple ? undefined : 'transparent';\n    const unprocessedRippleColor = android_ripple?.color ?? defaultRippleColor;\n    return IS_FABRIC\n      ? unprocessedRippleColor\n      : processColor(unprocessedRippleColor);\n  }, [android_ripple]);\n\n  return (\n    <GestureDetector gesture={gesture}>\n      <NativeButton\n        {...remainingProps}\n        ref={pressableRef}\n        hitSlop={appliedHitSlop}\n        enabled={isPressableEnabled}\n        touchSoundDisabled={android_disableSound ?? undefined}\n        rippleColor={rippleColor}\n        rippleRadius={android_ripple?.radius ?? undefined}\n        style={[pointerStyle, styleProp]}\n        testOnly_onPress={IS_TEST_ENV ? onPress : undefined}\n        testOnly_onPressIn={IS_TEST_ENV ? onPressIn : undefined}\n        testOnly_onPressOut={IS_TEST_ENV ? onPressOut : undefined}\n        testOnly_onLongPress={IS_TEST_ENV ? onLongPress : undefined}>\n        {childrenProp}\n        {__DEV__ ? (\n          <PressabilityDebugView color=\"red\" hitSlop={normalizedHitSlop} />\n        ) : null}\n      </NativeButton>\n    </GestureDetector>\n  );\n}\n"]}