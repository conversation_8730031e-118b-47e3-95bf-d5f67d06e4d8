{"version": 3, "sources": ["useAnimatedGesture.ts"], "names": ["<PERSON><PERSON><PERSON><PERSON>", "type", "gesture", "CALLBACK_TYPE", "BEGAN", "onBegin", "START", "onStart", "UPDATE", "onUpdate", "CHANGE", "onChange", "END", "onEnd", "FINALIZE", "onFinalize", "TOUCHES_DOWN", "onTouchesDown", "TOUCHES_MOVE", "onTouchesMove", "TOUCHES_UP", "onTouchesUp", "TOUCHES_CANCELLED", "onTouchesCancelled", "touchEventTypeToCallbackType", "eventType", "TouchEventType", "UNDEFINED", "runWorklet", "event", "args", "handler", "isWorklet", "console", "warn", "isStateChangeEvent", "oldState", "isTouchEvent", "useAnimatedGesture", "preparedGesture", "needsRebuild", "Reanimated", "sharedHandlersCallbacks", "useSharedValue", "lastUpdateEvent", "stateControllers", "callback", "currentCallback", "value", "i", "length", "handlerTag", "State", "UNDETERMINED", "state", "ACTIVE", "undefined", "FAILED", "CANCELLED", "GestureStateManager", "create", "changeEventCalculator", "useEvent", "animatedEventHandler", "animatedHandlers"], "mappings": ";;;;;;;AAAA;;AACA;;AAMA;;AAIA;;AACA;;AACA;;AAGA,SAASA,UAAT,CACEC,IADF,EAEEC,OAFF,EAGE;AACA;;AACA,UAAQD,IAAR;AACE,SAAKE,uBAAcC,KAAnB;AACE,aAAOF,OAAO,CAACG,OAAf;;AACF,SAAKF,uBAAcG,KAAnB;AACE,aAAOJ,OAAO,CAACK,OAAf;;AACF,SAAKJ,uBAAcK,MAAnB;AACE,aAAON,OAAO,CAACO,QAAf;;AACF,SAAKN,uBAAcO,MAAnB;AACE,aAAOR,OAAO,CAACS,QAAf;;AACF,SAAKR,uBAAcS,GAAnB;AACE,aAAOV,OAAO,CAACW,KAAf;;AACF,SAAKV,uBAAcW,QAAnB;AACE,aAAOZ,OAAO,CAACa,UAAf;;AACF,SAAKZ,uBAAca,YAAnB;AACE,aAAOd,OAAO,CAACe,aAAf;;AACF,SAAKd,uBAAce,YAAnB;AACE,aAAOhB,OAAO,CAACiB,aAAf;;AACF,SAAKhB,uBAAciB,UAAnB;AACE,aAAOlB,OAAO,CAACmB,WAAf;;AACF,SAAKlB,uBAAcmB,iBAAnB;AACE,aAAOpB,OAAO,CAACqB,kBAAf;AApBJ;AAsBD;;AAED,SAASC,4BAAT,CACEC,SADF,EAEiB;AACf;;AACA,UAAQA,SAAR;AACE,SAAKC,+BAAeV,YAApB;AACE,aAAOb,uBAAca,YAArB;;AACF,SAAKU,+BAAeR,YAApB;AACE,aAAOf,uBAAce,YAArB;;AACF,SAAKQ,+BAAeN,UAApB;AACE,aAAOjB,uBAAciB,UAArB;;AACF,SAAKM,+BAAeJ,iBAApB;AACE,aAAOnB,uBAAcmB,iBAArB;AARJ;;AAUA,SAAOnB,uBAAcwB,SAArB;AACD;;AAED,SAASC,UAAT,CACE3B,IADF,EAEEC,OAFF,EAGE2B,KAHF,EAIE,GAAGC,IAJL,EAKE;AACA;;AACA,QAAMC,OAAO,GAAG/B,UAAU,CAACC,IAAD,EAAOC,OAAP,CAA1B;;AACA,MAAIA,OAAO,CAAC8B,SAAR,CAAkB/B,IAAlB,CAAJ,EAA6B;AAC3B;AACA;AACA8B,IAAAA,OAAO,SAAP,IAAAA,OAAO,WAAP,YAAAA,OAAO,CAAGF,KAAH,EAAU,GAAGC,IAAb,CAAP;AACD,GAJD,MAIO,IAAIC,OAAJ,EAAa;AAClBE,IAAAA,OAAO,CAACC,IAAR,CAAa,uBAAW,6CAAX,CAAb;AACD;AACF;;AAED,SAASC,kBAAT,CACEN,KADF,EAEoC;AAClC,YADkC,CAElC;;AACA,SAAOA,KAAK,CAACO,QAAN,IAAkB,IAAzB;AACD;;AAED,SAASC,YAAT,CACER,KADF,EAE8B;AAC5B;;AACA,SAAOA,KAAK,CAACJ,SAAN,IAAmB,IAA1B;AACD;;AAEM,SAASa,kBAAT,CACLC,eADK,EAELC,YAFK,EAGL;AACA,MAAI,CAACC,6BAAL,EAAiB;AACf;AACD,GAHD,CAKA;AACA;AACA;;;AACA,QAAMC,uBAAuB,GAAGD,8BAAWE,cAAX,CAE9B,IAF8B,CAAhC,CARA,CAYA;;;AACA,QAAMC,eAAe,GAAGH,8BAAWE,cAAX,CAEtB,EAFsB,CAAxB,CAbA,CAiBA;;;AACA,QAAME,gBAA2C,GAAG,EAApD;;AAEA,QAAMC,QAAQ,GACZjB,KADe,IAEZ;AACH;;AAEA,UAAMkB,eAAe,GAAGL,uBAAuB,CAACM,KAAhD;;AACA,QAAI,CAACD,eAAL,EAAsB;AACpB;AACD;;AAED,SAAK,IAAIE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,eAAe,CAACG,MAApC,EAA4CD,CAAC,EAA7C,EAAiD;AAC/C,YAAM/C,OAAO,GAAG6C,eAAe,CAACE,CAAD,CAA/B;;AAEA,UAAIpB,KAAK,CAACsB,UAAN,KAAqBjD,OAAO,CAACiD,UAAjC,EAA6C;AAC3C;AACD;;AAED,UAAIhB,kBAAkB,CAACN,KAAD,CAAtB,EAA+B;AAC7B,YACEA,KAAK,CAACO,QAAN,KAAmBgB,aAAMC,YAAzB,IACAxB,KAAK,CAACyB,KAAN,KAAgBF,aAAMhD,KAFxB,EAGE;AACAwB,UAAAA,UAAU,CAACzB,uBAAcC,KAAf,EAAsBF,OAAtB,EAA+B2B,KAA/B,CAAV;AACD,SALD,MAKO,IACL,CAACA,KAAK,CAACO,QAAN,KAAmBgB,aAAMhD,KAAzB,IACCyB,KAAK,CAACO,QAAN,KAAmBgB,aAAMC,YAD3B,KAEAxB,KAAK,CAACyB,KAAN,KAAgBF,aAAMG,MAHjB,EAIL;AACA3B,UAAAA,UAAU,CAACzB,uBAAcG,KAAf,EAAsBJ,OAAtB,EAA+B2B,KAA/B,CAAV;AACAe,UAAAA,eAAe,CAACI,KAAhB,CAAsB9C,OAAO,CAACiD,UAA9B,IAA4CK,SAA5C;AACD,SAPM,MAOA,IACL3B,KAAK,CAACO,QAAN,KAAmBP,KAAK,CAACyB,KAAzB,IACAzB,KAAK,CAACyB,KAAN,KAAgBF,aAAMxC,GAFjB,EAGL;AACA,cAAIiB,KAAK,CAACO,QAAN,KAAmBgB,aAAMG,MAA7B,EAAqC;AACnC3B,YAAAA,UAAU,CAACzB,uBAAcS,GAAf,EAAoBV,OAApB,EAA6B2B,KAA7B,EAAoC,IAApC,CAAV;AACD;;AACDD,UAAAA,UAAU,CAACzB,uBAAcW,QAAf,EAAyBZ,OAAzB,EAAkC2B,KAAlC,EAAyC,IAAzC,CAAV;AACD,SARM,MAQA,IACL,CAACA,KAAK,CAACyB,KAAN,KAAgBF,aAAMK,MAAtB,IAAgC5B,KAAK,CAACyB,KAAN,KAAgBF,aAAMM,SAAvD,KACA7B,KAAK,CAACyB,KAAN,KAAgBzB,KAAK,CAACO,QAFjB,EAGL;AACA,cAAIP,KAAK,CAACO,QAAN,KAAmBgB,aAAMG,MAA7B,EAAqC;AACnC3B,YAAAA,UAAU,CAACzB,uBAAcS,GAAf,EAAoBV,OAApB,EAA6B2B,KAA7B,EAAoC,KAApC,CAAV;AACD;;AACDD,UAAAA,UAAU,CAACzB,uBAAcW,QAAf,EAAyBZ,OAAzB,EAAkC2B,KAAlC,EAAyC,KAAzC,CAAV;AACD;AACF,OA9BD,MA8BO,IAAIQ,YAAY,CAACR,KAAD,CAAhB,EAAyB;AAC9B,YAAI,CAACgB,gBAAgB,CAACI,CAAD,CAArB,EAA0B;AACxBJ,UAAAA,gBAAgB,CAACI,CAAD,CAAhB,GAAsBU,yCAAoBC,MAApB,CAA2B/B,KAAK,CAACsB,UAAjC,CAAtB;AACD;;AAED,YAAItB,KAAK,CAACJ,SAAN,KAAoBC,+BAAe2B,YAAvC,EAAqD;AACnDzB,UAAAA,UAAU,CACRJ,4BAA4B,CAACK,KAAK,CAACJ,SAAP,CADpB,EAERvB,OAFQ,EAGR2B,KAHQ,EAIRgB,gBAAgB,CAACI,CAAD,CAJR,CAAV;AAMD;AACF,OAbM,MAaA;AACLrB,QAAAA,UAAU,CAACzB,uBAAcK,MAAf,EAAuBN,OAAvB,EAAgC2B,KAAhC,CAAV;;AAEA,YAAI3B,OAAO,CAACS,QAAR,IAAoBT,OAAO,CAAC2D,qBAAhC,EAAuD;AAAA;;AACrDjC,UAAAA,UAAU,CACRzB,uBAAcO,MADN,EAERR,OAFQ,2BAGRA,OAAO,CAAC2D,qBAHA,0DAGR,2BAAA3D,OAAO,EACL2B,KADK,EAELe,eAAe,CAACI,KAAhB,CAAsB9C,OAAO,CAACiD,UAA9B,CAFK,CAHC,CAAV;AASAP,UAAAA,eAAe,CAACI,KAAhB,CAAsB9C,OAAO,CAACiD,UAA9B,IAA4CtB,KAA5C;AACD;AACF;AACF;AACF,GA7ED,CApBA,CAmGA;;;AACA,QAAMA,KAAK,GAAGY,8BAAWqB,QAAX,CACZhB,QADY,EAEZ,CAAC,6BAAD,EAAgC,uBAAhC,CAFY,EAGZN,YAHY,CAAd;;AAMAD,EAAAA,eAAe,CAACwB,oBAAhB,GAAuClC,KAAvC;AACAU,EAAAA,eAAe,CAACyB,gBAAhB,GAAmCtB,uBAAnC;AACD", "sourcesContent": ["import { HandlerCallbacks, CALLBACK_TYPE } from '../gesture';\nimport { Reanimated } from '../reanimatedWrapper';\nimport {\n  GestureTouchEvent,\n  GestureUpdateEvent,\n  GestureStateChangeEvent,\n} from '../../gestureHandlerCommon';\nimport {\n  GestureStateManager,\n  GestureStateManagerType,\n} from '../gestureStateManager';\nimport { State } from '../../../State';\nimport { TouchEventType } from '../../../TouchEventType';\nimport { tagMessage } from '../../../utils';\nimport { AttachedGestureState } from './types';\n\nfunction getHandler(\n  type: CALLBACK_TYPE,\n  gesture: HandlerCallbacks<Record<string, unknown>>\n) {\n  'worklet';\n  switch (type) {\n    case CALLBACK_TYPE.BEGAN:\n      return gesture.onBegin;\n    case CALLBACK_TYPE.START:\n      return gesture.onStart;\n    case CALLBACK_TYPE.UPDATE:\n      return gesture.onUpdate;\n    case CALLBACK_TYPE.CHANGE:\n      return gesture.onChange;\n    case CALLBACK_TYPE.END:\n      return gesture.onEnd;\n    case CALLBACK_TYPE.FINALIZE:\n      return gesture.onFinalize;\n    case CALLBACK_TYPE.TOUCHES_DOWN:\n      return gesture.onTouchesDown;\n    case CALLBACK_TYPE.TOUCHES_MOVE:\n      return gesture.onTouchesMove;\n    case CALLBACK_TYPE.TOUCHES_UP:\n      return gesture.onTouchesUp;\n    case CALLBACK_TYPE.TOUCHES_CANCELLED:\n      return gesture.onTouchesCancelled;\n  }\n}\n\nfunction touchEventTypeToCallbackType(\n  eventType: TouchEventType\n): CALLBACK_TYPE {\n  'worklet';\n  switch (eventType) {\n    case TouchEventType.TOUCHES_DOWN:\n      return CALLBACK_TYPE.TOUCHES_DOWN;\n    case TouchEventType.TOUCHES_MOVE:\n      return CALLBACK_TYPE.TOUCHES_MOVE;\n    case TouchEventType.TOUCHES_UP:\n      return CALLBACK_TYPE.TOUCHES_UP;\n    case TouchEventType.TOUCHES_CANCELLED:\n      return CALLBACK_TYPE.TOUCHES_CANCELLED;\n  }\n  return CALLBACK_TYPE.UNDEFINED;\n}\n\nfunction runWorklet(\n  type: CALLBACK_TYPE,\n  gesture: HandlerCallbacks<Record<string, unknown>>,\n  event: GestureStateChangeEvent | GestureUpdateEvent | GestureTouchEvent,\n  ...args: unknown[]\n) {\n  'worklet';\n  const handler = getHandler(type, gesture);\n  if (gesture.isWorklet[type]) {\n    // @ts-ignore Logic below makes sure the correct event is send to the\n    // correct handler.\n    handler?.(event, ...args);\n  } else if (handler) {\n    console.warn(tagMessage('Animated gesture callback must be a worklet'));\n  }\n}\n\nfunction isStateChangeEvent(\n  event: GestureUpdateEvent | GestureStateChangeEvent | GestureTouchEvent\n): event is GestureStateChangeEvent {\n  'worklet';\n  // @ts-ignore Yes, the oldState prop is missing on GestureTouchEvent, that's the point\n  return event.oldState != null;\n}\n\nfunction isTouchEvent(\n  event: GestureUpdateEvent | GestureStateChangeEvent | GestureTouchEvent\n): event is GestureTouchEvent {\n  'worklet';\n  return event.eventType != null;\n}\n\nexport function useAnimatedGesture(\n  preparedGesture: AttachedGestureState,\n  needsRebuild: boolean\n) {\n  if (!Reanimated) {\n    return;\n  }\n\n  // Hooks are called conditionally, but the condition is whether the\n  // react-native-reanimated is installed, which shouldn't change while running\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  const sharedHandlersCallbacks = Reanimated.useSharedValue<\n    HandlerCallbacks<Record<string, unknown>>[] | null\n  >(null);\n\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  const lastUpdateEvent = Reanimated.useSharedValue<\n    (GestureUpdateEvent | undefined)[]\n  >([]);\n\n  // not every gesture needs a state controller, init them lazily\n  const stateControllers: GestureStateManagerType[] = [];\n\n  const callback = (\n    event: GestureStateChangeEvent | GestureUpdateEvent | GestureTouchEvent\n  ) => {\n    'worklet';\n\n    const currentCallback = sharedHandlersCallbacks.value;\n    if (!currentCallback) {\n      return;\n    }\n\n    for (let i = 0; i < currentCallback.length; i++) {\n      const gesture = currentCallback[i];\n\n      if (event.handlerTag !== gesture.handlerTag) {\n        continue;\n      }\n\n      if (isStateChangeEvent(event)) {\n        if (\n          event.oldState === State.UNDETERMINED &&\n          event.state === State.BEGAN\n        ) {\n          runWorklet(CALLBACK_TYPE.BEGAN, gesture, event);\n        } else if (\n          (event.oldState === State.BEGAN ||\n            event.oldState === State.UNDETERMINED) &&\n          event.state === State.ACTIVE\n        ) {\n          runWorklet(CALLBACK_TYPE.START, gesture, event);\n          lastUpdateEvent.value[gesture.handlerTag] = undefined;\n        } else if (\n          event.oldState !== event.state &&\n          event.state === State.END\n        ) {\n          if (event.oldState === State.ACTIVE) {\n            runWorklet(CALLBACK_TYPE.END, gesture, event, true);\n          }\n          runWorklet(CALLBACK_TYPE.FINALIZE, gesture, event, true);\n        } else if (\n          (event.state === State.FAILED || event.state === State.CANCELLED) &&\n          event.state !== event.oldState\n        ) {\n          if (event.oldState === State.ACTIVE) {\n            runWorklet(CALLBACK_TYPE.END, gesture, event, false);\n          }\n          runWorklet(CALLBACK_TYPE.FINALIZE, gesture, event, false);\n        }\n      } else if (isTouchEvent(event)) {\n        if (!stateControllers[i]) {\n          stateControllers[i] = GestureStateManager.create(event.handlerTag);\n        }\n\n        if (event.eventType !== TouchEventType.UNDETERMINED) {\n          runWorklet(\n            touchEventTypeToCallbackType(event.eventType),\n            gesture,\n            event,\n            stateControllers[i]\n          );\n        }\n      } else {\n        runWorklet(CALLBACK_TYPE.UPDATE, gesture, event);\n\n        if (gesture.onChange && gesture.changeEventCalculator) {\n          runWorklet(\n            CALLBACK_TYPE.CHANGE,\n            gesture,\n            gesture.changeEventCalculator?.(\n              event,\n              lastUpdateEvent.value[gesture.handlerTag]\n            )\n          );\n\n          lastUpdateEvent.value[gesture.handlerTag] = event;\n        }\n      }\n    }\n  };\n\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  const event = Reanimated.useEvent(\n    callback,\n    ['onGestureHandlerStateChange', 'onGestureHandlerEvent'],\n    needsRebuild\n  );\n\n  preparedGesture.animatedEventHandler = event;\n  preparedGesture.animatedHandlers = sharedHandlersCallbacks;\n}\n"]}