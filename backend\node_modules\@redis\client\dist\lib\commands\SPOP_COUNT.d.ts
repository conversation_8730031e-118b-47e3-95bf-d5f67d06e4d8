import { CommandParser } from '../client/parser';
import { RedisArgument, BlobStringReply, NullReply } from '../RESP/types';
declare const _default: {
    readonly IS_READ_ONLY: false;
    /**
     * Constructs the SPOP command to remove and return multiple random members from a set
     *
     * @param parser - The command parser
     * @param key - The key of the set to pop from
     * @param count - The number of members to pop
     * @see https://redis.io/commands/spop/
     */
    readonly parseCommand: (this: void, parser: CommandParser, key: RedisArgument, count: number) => void;
    readonly transformReply: () => BlobStringReply | NullReply;
};
export default _default;
//# sourceMappingURL=SPOP_COUNT.d.ts.map