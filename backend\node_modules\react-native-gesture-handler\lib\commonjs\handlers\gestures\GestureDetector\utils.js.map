{"version": 3, "sources": ["utils.ts"], "names": ["ALLOWED_PROPS", "baseGestureHandlerWithDetectorProps", "tapGestureHandlerProps", "panGestureHandlerProps", "panGestureHandlerCustomNativeProps", "longPressGestureHandlerProps", "forceTouchGestureHandlerProps", "flingGestureHandlerProps", "hoverGestureHandlerProps", "nativeViewGestureHandlerProps", "convertToHandlerTag", "ref", "BaseGesture", "handlerTag", "current", "extractValidHandlerTags", "interactionGroup", "map", "filter", "tag", "extractGestureRelations", "gesture", "requireToFail", "config", "simultaneousWith", "blocksHandlers", "waitFor", "simultaneousHandlers", "checkGestureCallbacksForWorklets", "__DEV__", "runOnJS", "areSomeNotWorklets", "handlers", "isWorklet", "includes", "areSomeWorklets", "console", "error", "Reanimated", "undefined", "areAllNotWorklets", "warn", "validateDetectorChildren", "Platform", "OS", "REACT_NATIVE_VERSION", "wrapType", "minor", "major", "_reactInternals", "elementType", "_reactInternalFiber", "instance", "<PERSON><PERSON><PERSON><PERSON>", "findHostInstance_DEPRECATED", "_internalFiberInstanceHandleDEV", "sibling", "Error", "return", "useForceRender", "renderState", "setRenderState", "forceRender", "useWebEventHandlers", "onGestureHandlerEvent", "e", "nativeEvent", "onGestureHandlerStateChange"], "mappings": ";;;;;;;;;;;;AAAA;;AAEA;;AACA;;AAEA;;AACA;;AACA;;AACA;;AAIA;;AACA;;AACA;;AACA;;AAIA;;AACA;;AACA;;AACA;;AACA;;AACA;;AAGO,MAAMA,aAAa,GAAG,CAC3B,GAAGC,yDADwB,EAE3B,GAAGC,yCAFwB,EAG3B,GAAGC,yCAHwB,EAI3B,GAAGC,qDAJwB,EAK3B,GAAGC,qDALwB,EAM3B,GAAGC,uDANwB,EAO3B,GAAGC,6CAPwB,EAQ3B,GAAGC,sCARwB,EAS3B,GAAGC,uDATwB,CAAtB;;;AAYP,SAASC,mBAAT,CAA6BC,GAA7B,EAAsD;AACpD,MAAI,OAAOA,GAAP,KAAe,QAAnB,EAA6B;AAC3B,WAAOA,GAAP;AACD,GAFD,MAEO,IAAIA,GAAG,YAAYC,oBAAnB,EAAgC;AACrC,WAAOD,GAAG,CAACE,UAAX;AACD,GAFM,MAEA;AAAA;;AACL;AACA;AACA,oDAAOF,GAAG,CAACG,OAAX,iDAAO,aAAaD,UAApB,yEAAkC,CAAC,CAAnC;AACD;AACF;;AAED,SAASE,uBAAT,CAAiCC,gBAAjC,EAA6E;AAAA;;AAC3E,kCACEA,gBADF,aACEA,gBADF,iDACEA,gBAAgB,CAAEC,GAAlB,CAAsBP,mBAAtB,CADF,2DACE,uBAA4CQ,MAA5C,CAAoDC,GAAD,IAASA,GAAG,GAAG,CAAlE,CADF,yEAC0E,EAD1E;AAGD;;AAEM,SAASC,uBAAT,CAAiCC,OAAjC,EAAuD;AAC5D,QAAMC,aAAa,GAAGP,uBAAuB,CAACM,OAAO,CAACE,MAAR,CAAeD,aAAhB,CAA7C;AACA,QAAME,gBAAgB,GAAGT,uBAAuB,CAC9CM,OAAO,CAACE,MAAR,CAAeC,gBAD+B,CAAhD;AAGA,QAAMC,cAAc,GAAGV,uBAAuB,CAACM,OAAO,CAACE,MAAR,CAAeE,cAAhB,CAA9C;AAEA,SAAO;AACLC,IAAAA,OAAO,EAAEJ,aADJ;AAELK,IAAAA,oBAAoB,EAAEH,gBAFjB;AAGLC,IAAAA,cAAc,EAAEA;AAHX,GAAP;AAKD;;AAEM,SAASG,gCAAT,CAA0CP,OAA1C,EAAgE;AACrE,MAAI,CAACQ,OAAL,EAAc;AACZ;AACD,GAHoE,CAIrE;AACA;;;AACA,MAAIR,OAAO,CAACE,MAAR,CAAeO,OAAnB,EAA4B;AAC1B;AACD;;AAED,QAAMC,kBAAkB,GAAGV,OAAO,CAACW,QAAR,CAAiBC,SAAjB,CAA2BC,QAA3B,CAAoC,KAApC,CAA3B;AACA,QAAMC,eAAe,GAAGd,OAAO,CAACW,QAAR,CAAiBC,SAAjB,CAA2BC,QAA3B,CAAoC,IAApC,CAAxB,CAXqE,CAarE;AACA;;AACA,MAAIH,kBAAkB,IAAII,eAA1B,EAA2C;AACzCC,IAAAA,OAAO,CAACC,KAAR,CACE,uBACG,2QADH,CADF;AAKD;;AAED,MAAIC,kCAAeC,SAAnB,EAA8B;AAC5B;AACA;AACD;;AAED,QAAMC,iBAAiB,GAAG,CAACL,eAAD,IAAoBJ,kBAA9C,CA5BqE,CA6BrE;AACA;;AACA,MAAIS,iBAAiB,IAAI,CAAC,uBAA1B,EAAuC;AACrCJ,IAAAA,OAAO,CAACK,IAAR,CACE,uBACG,0OADH,CADF;AAKD;AACF,C,CAED;;;AACO,SAASC,wBAAT,CAAkC/B,GAAlC,EAA4C;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAIkB,OAAO,IAAIc,sBAASC,EAAT,KAAgB,KAA/B,EAAsC;AACpC,UAAMC,oBAAoB,GAAG,mDAA7B,CADoC,CAEpC;;AACA,UAAMC,QAAQ,GACZD,oBAAoB,CAACE,KAArB,GAA6B,EAA7B,IAAmCF,oBAAoB,CAACG,KAArB,GAA6B,CAAhE,GACI;AACArC,IAAAA,GAAG,CAACsC,eAAJ,CAAoBC,WAFxB,GAGI;AACAvC,IAAAA,GAAG,CAACwC,mBAAJ,CAAwBD,WAL9B,CAHoC,CASpC;;AACA,QAAIE,QAAQ,GACVC,uBAAWC,2BAAX,CACE3C,GADF,EAEE4C,+BAHJ,CAVoC,CAepC;;;AACA,WAAOH,QAAQ,IAAIA,QAAQ,CAACF,WAAT,KAAyBJ,QAA5C,EAAsD;AACpD;AACA,UAAIM,QAAQ,CAACI,OAAb,EAAsB;AACpB,cAAM,IAAIC,KAAJ,CACJ,mPADI,CAAN;AAGD,OANmD,CAQpD;;;AACAL,MAAAA,QAAQ,GAAGA,QAAQ,CAACM,MAApB;AACD;AACF;AACF;;AAEM,SAASC,cAAT,GAA0B;AAC/B,QAAM,CAACC,WAAD,EAAcC,cAAd,IAAgC,qBAAS,KAAT,CAAtC;AACA,QAAMC,WAAW,GAAG,wBAAY,MAAM;AACpCD,IAAAA,cAAc,CAAC,CAACD,WAAF,CAAd;AACD,GAFmB,EAEjB,CAACA,WAAD,EAAcC,cAAd,CAFiB,CAApB;AAIA,SAAOC,WAAP;AACD;;AAEM,SAASC,mBAAT,GAA+B;AACpC,SAAO,mBAAwB;AAC7BC,IAAAA,qBAAqB,EAAGC,CAAD,IAAyC;AAC9D,gDAAsBA,CAAC,CAACC,WAAxB;AACD,KAH4B;AAI7BC,IAAAA,2BAA2B,EAAE,mEACxBF,CAAD,IAAyC;AACvC,gDAAsBA,CAAC,CAACC,WAAxB;AACD,KAHwB,GAIzB3B;AARyB,GAAxB,CAAP;AAUD", "sourcesContent": ["import { Platform } from 'react-native';\n\nimport { isTestEnv, tagMessage } from '../../../utils';\nimport { GestureRef, BaseGesture, GestureType } from '../gesture';\n\nimport { flingGestureHandlerProps } from '../../FlingGestureHandler';\nimport { forceTouchGestureHandlerProps } from '../../ForceTouchGestureHandler';\nimport { longPressGestureHandlerProps } from '../../LongPressGestureHandler';\nimport {\n  panGestureHandlerProps,\n  panGestureHandlerCustomNativeProps,\n} from '../../PanGestureHandler';\nimport { tapGestureHandlerProps } from '../../TapGestureHandler';\nimport { hoverGestureHandlerProps } from '../hoverGesture';\nimport { nativeViewGestureHandlerProps } from '../../NativeViewGestureHandler';\nimport {\n  HandlerStateChangeEvent,\n  baseGestureHandlerWithDetectorProps,\n} from '../../gestureHandlerCommon';\nimport { isNewWebImplementationEnabled } from '../../../EnableNewWebImplementation';\nimport { getReactNativeVersion } from '../../../getReactNativeVersion';\nimport { RNRenderer } from '../../../RNRenderer';\nimport { useCallback, useRef, useState } from 'react';\nimport { Reanimated } from '../reanimatedWrapper';\nimport { onGestureHandlerEvent } from '../eventReceiver';\nimport { WebEventHandler } from './types';\n\nexport const ALLOWED_PROPS = [\n  ...baseGestureHandlerWithDetectorProps,\n  ...tapGestureHandlerProps,\n  ...panGestureHandlerProps,\n  ...panGestureHandlerCustomNativeProps,\n  ...longPressGestureHandlerProps,\n  ...forceTouchGestureHandlerProps,\n  ...flingGestureHandlerProps,\n  ...hoverGestureHandlerProps,\n  ...nativeViewGestureHandlerProps,\n];\n\nfunction convertToHandlerTag(ref: GestureRef): number {\n  if (typeof ref === 'number') {\n    return ref;\n  } else if (ref instanceof BaseGesture) {\n    return ref.handlerTag;\n  } else {\n    // @ts-ignore in this case it should be a ref either to gesture object or\n    // a gesture handler component, in both cases handlerTag property exists\n    return ref.current?.handlerTag ?? -1;\n  }\n}\n\nfunction extractValidHandlerTags(interactionGroup: GestureRef[] | undefined) {\n  return (\n    interactionGroup?.map(convertToHandlerTag)?.filter((tag) => tag > 0) ?? []\n  );\n}\n\nexport function extractGestureRelations(gesture: GestureType) {\n  const requireToFail = extractValidHandlerTags(gesture.config.requireToFail);\n  const simultaneousWith = extractValidHandlerTags(\n    gesture.config.simultaneousWith\n  );\n  const blocksHandlers = extractValidHandlerTags(gesture.config.blocksHandlers);\n\n  return {\n    waitFor: requireToFail,\n    simultaneousHandlers: simultaneousWith,\n    blocksHandlers: blocksHandlers,\n  };\n}\n\nexport function checkGestureCallbacksForWorklets(gesture: GestureType) {\n  if (!__DEV__) {\n    return;\n  }\n  // If a gesture is explicitly marked to run on the JS thread there is no need to check\n  // if callbacks are worklets as the user is aware they will be ran on the JS thread\n  if (gesture.config.runOnJS) {\n    return;\n  }\n\n  const areSomeNotWorklets = gesture.handlers.isWorklet.includes(false);\n  const areSomeWorklets = gesture.handlers.isWorklet.includes(true);\n\n  // If some of the callbacks are worklets and some are not, and the gesture is not\n  // explicitly marked with `.runOnJS(true)` show an error\n  if (areSomeNotWorklets && areSomeWorklets) {\n    console.error(\n      tagMessage(\n        `Some of the callbacks in the gesture are worklets and some are not. Either make sure that all calbacks are marked as 'worklet' if you wish to run them on the UI thread or use '.runOnJS(true)' modifier on the gesture explicitly to run all callbacks on the JS thread.`\n      )\n    );\n  }\n\n  if (Reanimated === undefined) {\n    // If Reanimated is not available, we can't run worklets, so we shouldn't show the warning\n    return;\n  }\n\n  const areAllNotWorklets = !areSomeWorklets && areSomeNotWorklets;\n  // If none of the callbacks are worklets and the gesture is not explicitly marked with\n  // `.runOnJS(true)` show a warning\n  if (areAllNotWorklets && !isTestEnv()) {\n    console.warn(\n      tagMessage(\n        `None of the callbacks in the gesture are worklets. If you wish to run them on the JS thread use '.runOnJS(true)' modifier on the gesture to make this explicit. Otherwise, mark the callbacks as 'worklet' to run them on the UI thread.`\n      )\n    );\n  }\n}\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport function validateDetectorChildren(ref: any) {\n  // Finds the first native view under the Wrap component and traverses the fiber tree upwards\n  // to check whether there is more than one native view as a pseudo-direct child of GestureDetector\n  // i.e. this is not ok:\n  //            Wrap\n  //             |\n  //            / \\\n  //           /   \\\n  //          /     \\\n  //         /       \\\n  //   NativeView  NativeView\n  //\n  // but this is fine:\n  //            Wrap\n  //             |\n  //         NativeView\n  //             |\n  //            / \\\n  //           /   \\\n  //          /     \\\n  //         /       \\\n  //   NativeView  NativeView\n  if (__DEV__ && Platform.OS !== 'web') {\n    const REACT_NATIVE_VERSION = getReactNativeVersion();\n    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n    const wrapType =\n      REACT_NATIVE_VERSION.minor > 63 || REACT_NATIVE_VERSION.major > 0\n        ? // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n          ref._reactInternals.elementType\n        : // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n          ref._reactInternalFiber.elementType;\n    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n    let instance =\n      RNRenderer.findHostInstance_DEPRECATED(\n        ref\n      )._internalFiberInstanceHandleDEV;\n\n    // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n    while (instance && instance.elementType !== wrapType) {\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n      if (instance.sibling) {\n        throw new Error(\n          'GestureDetector has more than one native view as its children. This can happen if you are using a custom component that renders multiple views, like React.Fragment. You should wrap content of GestureDetector with a <View> or <Animated.View>.'\n        );\n      }\n\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-member-access\n      instance = instance.return;\n    }\n  }\n}\n\nexport function useForceRender() {\n  const [renderState, setRenderState] = useState(false);\n  const forceRender = useCallback(() => {\n    setRenderState(!renderState);\n  }, [renderState, setRenderState]);\n\n  return forceRender;\n}\n\nexport function useWebEventHandlers() {\n  return useRef<WebEventHandler>({\n    onGestureHandlerEvent: (e: HandlerStateChangeEvent<unknown>) => {\n      onGestureHandlerEvent(e.nativeEvent);\n    },\n    onGestureHandlerStateChange: isNewWebImplementationEnabled()\n      ? (e: HandlerStateChangeEvent<unknown>) => {\n          onGestureHandlerEvent(e.nativeEvent);\n        }\n      : undefined,\n  });\n}\n"]}