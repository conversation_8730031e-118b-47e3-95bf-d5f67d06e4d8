{"version": 3, "sources": ["gestureHandlerRootHOC.tsx"], "names": ["gestureHandlerRootHOC", "Component", "containerStyles", "Wrapper", "props", "styles", "container", "displayName", "name", "StyleSheet", "create", "flex"], "mappings": ";;;;;;;AAAA;;AACA;;AACA;;AACA;;;;;;;;AAEe,SAASA,qBAAT,CACbC,SADa,EAEbC,eAFa,EAGW;AACxB,WAASC,OAAT,CAAiBC,KAAjB,EAA2B;AACzB,wBACE,oBAAC,+BAAD;AAAwB,MAAA,KAAK,EAAE,CAACC,MAAM,CAACC,SAAR,EAAmBJ,eAAnB;AAA/B,oBACE,oBAAC,SAAD,EAAeE,KAAf,CADF,CADF;AAKD;;AAEDD,EAAAA,OAAO,CAACI,WAAR,GAAuB,yBACrBN,SAAS,CAACM,WAAV,IAAyBN,SAAS,CAACO,IACpC,GAFD,CATwB,CAaxB;;AACA,qCAAqBL,OAArB,EAA8BF,SAA9B;AAEA,SAAOE,OAAP;AACD;;AAED,MAAME,MAAM,GAAGI,wBAAWC,MAAX,CAAkB;AAC/BJ,EAAAA,SAAS,EAAE;AAAEK,IAAAA,IAAI,EAAE;AAAR;AADoB,CAAlB,CAAf", "sourcesContent": ["import * as React from 'react';\nimport { StyleSheet, StyleProp, ViewStyle } from 'react-native';\nimport hoistNonReactStatics from 'hoist-non-react-statics';\nimport GestureHandlerRootView from './GestureHandlerRootView';\n\nexport default function gestureHandlerRootHOC<P extends object>(\n  Component: React.ComponentType<P>,\n  containerStyles?: StyleProp<ViewStyle>\n): React.ComponentType<P> {\n  function Wrapper(props: P) {\n    return (\n      <GestureHandlerRootView style={[styles.container, containerStyles]}>\n        <Component {...props} />\n      </GestureHandlerRootView>\n    );\n  }\n\n  Wrapper.displayName = `gestureHandlerRootHOC(${\n    Component.displayName || Component.name\n  })`;\n\n  // @ts-ignore - hoistNonReactStatics uses old version of @types/react\n  hoistNonReactStatics(Wrapper, Component);\n\n  return Wrapper;\n}\n\nconst styles = StyleSheet.create({\n  container: { flex: 1 },\n});\n"]}